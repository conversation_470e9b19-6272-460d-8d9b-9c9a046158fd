# 智能匹配引擎使用指南

## 🎯 功能概述

智能匹配引擎是婚介所系统的核心功能，基于AI技术为会员提供精准的匹配推荐。系统结合了语义相似度分析、规则引擎过滤、活跃度评估等多维度算法，为红娘提供高质量的匹配建议。

## 🏗️ 系统架构

### 数据库设计
```sql
-- 会员画像向量表
member_embeddings (member_id, profile_vector, vector_version)

-- 匹配记录表  
match_records (id, member_a_id, member_b_id, match_score, reason, ice_breakers, status)

-- 会员偏好配置表
member_preferences (member_id, age_min, age_max, height_min, height_max, preferred_cities)
```

### 核心组件
- **MatchingEngineService**: 智能匹配引擎核心服务
- **MatchRecordService**: 匹配记录管理服务
- **ContentGeneratorService**: AI内容生成服务
- **MatchmakerDashboard**: 红娘工作台前端界面

## 🔧 核心算法

### 1. 综合评分公式
```
最终分数 = 语义相似度 × 0.45 + 硬性条件 × 0.25 + 活跃度 × 0.15 + 新用户加权 × 0.05 + 红娘优先级 × 0.10
```

### 2. 硬性条件匹配
- **年龄匹配** (30%): 年龄差≤3岁得满分，≤5岁得0.67分，≤8岁得0.33分
- **城市匹配** (40%): 同城得满分，异地得0分
- **学历匹配** (30%): 学历差≤1级得满分，≤2级得0.5分

### 3. 活跃度评估
- 7天内活跃: 1.0分
- 30天内活跃: 0.8分  
- 90天内活跃: 0.5分
- 超过90天: 0.2分

## 🚀 使用流程

### 1. 红娘工作台操作
1. 访问 `http://localhost:3000/matchmaker-dashboard`
2. 输入目标会员ID
3. 选择推荐数量（5/10/20个）
4. 点击"获取推荐"
5. 查看匹配结果和AI生成内容
6. 标记匹配状态（查看/回复/约见/成功/失败）

### 2. API调用方式
```javascript
// 获取匹配推荐
const matches = await matchingApi.getMatches({
  memberId: 1001,
  topK: 10,
  matchmakerId: 1,
  includeAiContent: true
})

// 提交反馈
await matchingApi.submitFeedback({
  matchId: 123,
  action: 'reply',
  operatorId: 1
})
```

## 📊 匹配状态管理

### 状态流转
```
0-推荐 → 1-查看 → 2-回复 → 3-约见 → 4-成功
                                    ↘ 5-失败
```

### 统计指标
- **今日推荐数**: 当日生成的匹配推荐总数
- **查看率**: 被查看的推荐占总推荐的比例
- **回复率**: 获得回复的推荐占总推荐的比例  
- **约见率**: 成功约见的推荐占总推荐的比例
- **成功率**: 最终成功的推荐占总推荐的比例

## 🤖 AI内容生成

### 生成内容类型
1. **匹配推荐理由**: 基于双方信息分析匹配优势
2. **破冰开场白**: 3种风格的聊天开场白
3. **约会建议**: 个性化的约会地点推荐

### 生成逻辑
- 使用DeepSeek大语言模型
- 专业的婚介行业提示词
- 结构化JSON输出格式
- 自动缓存避免重复生成

## 🎛️ 配置参数

### 匹配权重配置
```sql
INSERT INTO system_config VALUES
('matching.weights.semantic', '0.45', 'number', '语义相似度权重'),
('matching.weights.hard_conditions', '0.25', 'number', '硬性条件权重'),
('matching.weights.activity', '0.15', 'number', '活跃度权重'),
('matching.candidate.pool_size', '100', 'number', '候选池大小'),
('matching.result.top_k', '10', 'number', '默认返回匹配数量');
```

### 偏好过滤配置
会员可设置个人偏好：
- 年龄范围偏好
- 身高范围偏好  
- 最低学历要求
- 最低收入要求
- 偏好城市列表
- 不接受条件列表

## 📈 性能优化

### 1. 向量检索优化
- 使用向量数据库（如pgvector）加速相似度检索
- 批量向量化处理
- 向量缓存机制

### 2. 匹配缓存策略
- 匹配结果缓存30天
- AI生成内容缓存避免重复调用
- 热门会员匹配预计算

### 3. 数据库优化
- 关键字段建立索引
- 分页查询优化
- 读写分离部署

## 🔍 监控告警

### 关键指标监控
- 匹配推荐生成耗时
- AI内容生成成功率
- 各阶段转化率异常
- API调用成本控制

### 告警规则
```python
if reply_rate < 20:
    alert("回复率异常偏低")
if api_cost > daily_budget * 1.2:
    alert("API成本超预算")
```

## 🚧 待优化功能

### 1. 向量化模型集成
- 集成m3e-small中文向量模型
- 实现真正的语义相似度检索
- 支持用户画像向量化

### 2. 机器学习优化
- 基于历史匹配数据训练排序模型
- A/B测试不同权重配置
- 个性化推荐算法

### 3. 实时推荐
- WebSocket实时推送新匹配
- 在线状态感知
- 即时消息集成

## 📝 使用示例

### 完整匹配流程示例
```javascript
// 1. 获取匹配推荐
const matchRequest = {
  memberId: 1001,
  topK: 10,
  matchmakerId: 1,
  includeAiContent: true,
  filter: {
    minScore: 0.6,
    city: '杭州',
    ageMin: 25,
    ageMax: 35
  }
}

const matches = await matchingApi.getMatches(matchRequest)

// 2. 展示匹配结果
matches.data.forEach(match => {
  console.log(`推荐: ${match.candidate.name}`)
  console.log(`匹配度: ${(match.matchScore * 100).toFixed(1)}%`)
  console.log(`推荐理由: ${match.reason}`)
  console.log(`破冰话术: ${match.iceBreakers.join(', ')}`)
})

// 3. 提交反馈
await matchingApi.submitFeedback({
  matchId: matches.data[0].matchId,
  action: 'view',
  operatorId: 1
})

// 4. 查看统计
const stats = await matchingApi.getTodayStatistics(1)
console.log(`今日推荐: ${stats.data.totalMatches}`)
console.log(`回复率: ${stats.data.replyRate.toFixed(1)}%`)
```

## 🎉 总结

智能匹配引擎为婚介所提供了完整的AI赋能解决方案，从数据录入到智能匹配，再到内容生成，形成了完整的业务闭环。通过持续优化算法和用户体验，系统将不断提升匹配成功率和用户满意度。
