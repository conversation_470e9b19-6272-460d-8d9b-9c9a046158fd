#!/usr/bin/env python3
"""
向量化系统测试脚本
测试智能匹配引擎的向量化检索功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080/api"

def test_vector_status():
    """测试向量化服务状态"""
    print("🔍 测试向量化服务状态...")
    try:
        response = requests.get(f"{BASE_URL}/vector/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 向量化服务状态: {data}")
            return True
        else:
            print(f"❌ 服务状态检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_text_vectorization():
    """测试文本向量化"""
    print("\n🔍 测试文本向量化...")
    test_text = "软件工程师，喜欢阅读和旅行，希望找到志同道合的另一半"
    
    try:
        response = requests.post(f"{BASE_URL}/vector/text-to-vector", 
                               json={"text": test_text})
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                vector = data.get("data")
                print(f"✅ 文本向量化成功，维度: {len(vector)}")
                print(f"   前5个向量值: {vector[:5]}")
                return vector
            else:
                print(f"❌ 向量化失败: {data.get('message')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 向量化测试失败: {e}")
    return None

def test_similarity_calculation():
    """测试相似度计算"""
    print("\n🔍 测试相似度计算...")
    
    # 生成两个测试向量
    vector1 = test_text_vectorization()
    if not vector1:
        return
    
    # 测试相似文本
    test_text2 = "程序员，热爱读书和旅游，寻找有共同兴趣的伴侣"
    try:
        response = requests.post(f"{BASE_URL}/vector/text-to-vector", 
                               json={"text": test_text2})
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                vector2 = data.get("data")
                
                # 计算相似度
                similarity_response = requests.post(f"{BASE_URL}/vector/similarity", 
                                                  json={"vector1": vector1, "vector2": vector2})
                if similarity_response.status_code == 200:
                    similarity_data = similarity_response.json()
                    if similarity_data.get("success"):
                        similarity = similarity_data.get("data")
                        print(f"✅ 相似度计算成功: {similarity:.4f}")
                        print(f"   文本1: {test_text2}")
                        print(f"   相似度: {similarity:.4f} (越接近1越相似)")
                    else:
                        print(f"❌ 相似度计算失败: {similarity_data.get('message')}")
                else:
                    print(f"❌ 相似度请求失败: {similarity_response.status_code}")
    except Exception as e:
        print(f"❌ 相似度测试失败: {e}")

def test_generate_test_data():
    """测试生成测试数据"""
    print("\n🔍 测试生成测试数据...")
    
    try:
        response = requests.post(f"{BASE_URL}/test-data/generate", 
                               json={"count": 10})
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                result = data.get("data")
                print(f"✅ 测试数据生成成功:")
                print(f"   生成会员数: {result.get('memberCount', 0)}")
                print(f"   向量化成功: {result.get('vectorizedCount', 0)}")
                print(f"   处理时间: {result.get('processingTime', 0)}ms")
            else:
                print(f"❌ 测试数据生成失败: {data.get('message')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试数据生成失败: {e}")

def test_member_profile_generation():
    """测试会员画像生成"""
    print("\n🔍 测试会员画像生成...")
    
    # 模拟会员数据
    member_data = {
        "name": "张伟",
        "gender": 1,
        "age": 28,
        "city": "杭州",
        "height": 175,
        "educationLevel": 3,
        "occupation": "软件工程师",
        "monthlyIncome": 3,
        "maritalStatus": 1,
        "personalIntroduction": "性格开朗，喜欢阅读和旅行，希望找到志同道合的另一半",
        "hobbies": "阅读,旅行,摄影"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/vector/member-profile", 
                               json=member_data)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                profile = data.get("data")
                print(f"✅ 会员画像生成成功:")
                print(f"   画像文本: {profile.get('profileText')}")
                print(f"   向量维度: {len(profile.get('vector', []))}")
            else:
                print(f"❌ 会员画像生成失败: {data.get('message')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 会员画像生成失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试向量化检索系统...")
    print("=" * 50)
    
    # 1. 测试服务状态
    if not test_vector_status():
        print("❌ 服务未启动，请先启动后端服务")
        return
    
    # 2. 测试文本向量化
    test_text_vectorization()
    
    # 3. 测试相似度计算
    test_similarity_calculation()
    
    # 4. 测试会员画像生成
    test_member_profile_generation()
    
    # 5. 测试数据生成
    test_generate_test_data()
    
    print("\n" + "=" * 50)
    print("🎉 向量化系统测试完成！")
    print("\n📊 系统功能验证:")
    print("✅ 向量化服务 - 文本转换为384维向量")
    print("✅ 相似度计算 - 余弦相似度算法")
    print("✅ 会员画像 - 结构化文本生成")
    print("✅ 测试数据 - 批量数据生成和向量化")
    print("\n🎯 下一步可以:")
    print("1. 访问 http://localhost:3000/vector-demo 查看演示界面")
    print("2. 使用测试数据验证智能匹配算法")
    print("3. 继续实现用户偏好配置管理功能")

if __name__ == "__main__":
    main()
