# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 响应格式

所有API接口都返回统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 响应码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 会员管理接口

### 1. 上传表格图片进行OCR识别

**接口地址**: `POST /member/upload-form`

**请求方式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | ✓ | 表格图片文件（JPG/PNG/PDF，最大10MB） |
| operator | String | | 操作人账号，默认为"system" |

**响应示例**:
```json
{
  "code": 200,
  "message": "表格识别完成",
  "data": {
    "confidence": 0.85,
    "fields": {
      "name": "张三",
      "gender": 1,
      "age": 28,
      "height": 175,
      "educationLevel": 3,
      "marriageStatus": 1,
      "phone": "13800138000"
    },
    "validationErrors": [
      {
        "fieldName": "phone",
        "errorType": "FORMAT_ERROR",
        "errorMessage": "手机号格式错误",
        "originalValue": "13800138000",
        "suggestedValue": null
      }
    ],
    "aiCorrections": {
      "phone": "13812345678"
    },
    "rawResponse": "..."
  },
  "timestamp": 1640995200000
}
```

**流程说明**:
1. 上传图片文件
2. 调用Qwen3-VL进行OCR识别
3. 规则引擎校验数据合法性
4. 如有错误，调用DeepSeek进行AI纠错
5. 返回识别结果、校验错误和AI建议

### 2. 确认并保存会员信息

**接口地址**: `POST /member/confirm-save`

**请求方式**: `application/json`

**请求参数**:
```json
{
  "fields": {
    "name": "张三",
    "gender": 1,
    "age": 28,
    "height": 175,
    "educationLevel": 3,
    "marriageStatus": 1,
    "phone": "13812345678"
  },
  "rawOcrDataId": 123,
  "operator": "红娘001"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fields | Object | ✓ | 确认后的会员字段数据 |
| rawOcrDataId | Long | | 原始OCR数据ID |
| operator | String | | 操作人账号 |

**响应示例**:
```json
{
  "code": 200,
  "message": "会员信息保存成功",
  "data": {
    "id": 1001,
    "name": "张三",
    "gender": 1,
    "age": 28,
    "height": 175,
    "educationLevel": 3,
    "marriageStatus": 1,
    "phone": "13812345678",
    "status": 1,
    "createTime": "2024-01-01T10:00:00",
    "creator": "红娘001"
  },
  "timestamp": 1640995200000
}
```

### 3. 查询会员信息

**接口地址**: `GET /member/{id}`

**请求方式**: `GET`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | ✓ | 会员ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "name": "张三",
    "gender": 1,
    "age": 28,
    "height": 175,
    "educationLevel": 3,
    "marriageStatus": 1,
    "phone": "13812345678",
    "status": 1,
    "createTime": "2024-01-01T10:00:00",
    "creator": "红娘001"
  },
  "timestamp": 1640995200000
}
```

### 4. 更新会员信息

**接口地址**: `PUT /member/{id}`

**请求方式**: `application/json`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | ✓ | 会员ID |

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operator | String | | 操作人账号 |

**请求体**: 会员信息对象（同查询响应格式）

### 5. 删除会员信息

**接口地址**: `DELETE /member/{id}`

**请求方式**: `DELETE`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | ✓ | 会员ID |

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operator | String | | 操作人账号 |

### 6. 健康检查

**接口地址**: `GET /member/health`

**请求方式**: `GET`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "服务正常运行",
  "timestamp": 1640995200000
}
```

## 数据字典

### 性别枚举
| 值 | 说明 |
|----|------|
| 1 | 男 |
| 2 | 女 |

### 学历枚举
| 值 | 说明 |
|----|------|
| 1 | 高中 |
| 2 | 专科 |
| 3 | 本科 |
| 4 | 硕士 |
| 5 | 博士 |
| 6 | 其他 |

### 婚姻状况枚举
| 值 | 说明 |
|----|------|
| 1 | 未婚 |
| 2 | 离异 |
| 3 | 丧偶 |

### 月收入枚举
| 值 | 说明 |
|----|------|
| 1 | 2000以下 |
| 2 | 2000-4000 |
| 3 | 4000-8000 |
| 4 | 8000-20000 |
| 5 | 20000以上 |

### 单位性质枚举
| 值 | 说明 |
|----|------|
| 1 | 国企 |
| 2 | 外企 |
| 3 | 私企 |
| 4 | 事业单位 |
| 5 | 公务员 |
| 6 | 自由职业 |

### 房产情况枚举
| 值 | 说明 |
|----|------|
| 1 | 有房 |
| 2 | 无房 |
| 3 | 按揭 |

### 车产情况枚举
| 值 | 说明 |
|----|------|
| 1 | 有车 |
| 2 | 无车 |

## 错误处理

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 请选择要上传的文件 | 文件参数为空 |
| 400 | 只支持JPG、PNG、PDF格式的文件 | 文件格式不支持 |
| 400 | 文件大小不能超过10MB | 文件过大 |
| 400 | 姓名不能为空 | 必填字段验证失败 |
| 404 | 资源不存在 | 会员ID不存在 |
| 500 | OCR识别失败 | OCR服务异常 |
| 500 | 保存失败 | 数据库操作异常 |

### 错误响应示例
```json
{
  "code": 400,
  "message": "只支持JPG、PNG、PDF格式的文件",
  "data": null,
  "timestamp": 1640995200000
}
```

## 调用示例

### JavaScript/Axios示例

```javascript
// 上传表格
const uploadForm = async (file, operator = 'system') => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('operator', operator)
  
  const response = await axios.post('/api/member/upload-form', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000
  })
  
  return response.data
}

// 保存会员
const saveMember = async (fields, rawOcrDataId, operator = 'system') => {
  const response = await axios.post('/api/member/confirm-save', {
    fields,
    rawOcrDataId,
    operator
  })
  
  return response.data
}
```

### cURL示例

```bash
# 上传表格
curl -X POST \
  http://localhost:8080/api/member/upload-form \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/form.jpg' \
  -F 'operator=红娘001'

# 保存会员
curl -X POST \
  http://localhost:8080/api/member/confirm-save \
  -H 'Content-Type: application/json' \
  -d '{
    "fields": {
      "name": "张三",
      "gender": 1,
      "age": 28
    },
    "operator": "红娘001"
  }'
```
