-- 婚介所后台管理系统数据库设计
-- MySQL 8.0+ 兼容语法

-- 创建数据库
CREATE DATABASE IF NOT EXISTS matchmaking_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE matchmaking_system;

-- 1. 原始OCR数据表
CREATE TABLE raw_ocr_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    member_id BIGINT COMMENT '关联会员ID',
    image_url VARCHAR(500) NOT NULL COMMENT '原始图片URL',
    raw_json TEXT NOT NULL COMMENT 'OCR识别原始JSON结果',
    ocr_confidence DECIMAL(5,2) DEFAULT 0.00 COMMENT 'OCR识别置信度',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_member_id (member_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始OCR识别数据表';

-- 2. 会员信息表
CREATE TABLE member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 基础信息
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender TINYINT NOT NULL COMMENT '性别：1-男，2-女',
    birth_date DATE COMMENT '出生年月',
    age INT NOT NULL COMMENT '年龄',
    ethnicity VARCHAR(20) DEFAULT '汉' COMMENT '民族',
    
    -- 外貌信息
    height INT COMMENT '身高(cm)',
    weight INT COMMENT '体重(kg)',
    
    -- 学历职业
    education_level TINYINT COMMENT '学历：1-高中，2-专科，3-本科，4-硕士，5-博士，6-其他',
    major VARCHAR(100) COMMENT '专业',
    occupation VARCHAR(100) COMMENT '职业',
    company_type TINYINT COMMENT '单位性质：1-国企，2-外企，3-私企，4-事业单位，5-公务员，6-自由职业',
    monthly_income TINYINT COMMENT '月收入：1-2000以下，2-2000-4000，3-4000-8000，4-8000-20000，5-20000以上',
    
    -- 婚姻房产
    marriage_status TINYINT NOT NULL COMMENT '婚姻状况：1-未婚，2-离异，3-丧偶',
    house_status TINYINT COMMENT '房产情况：1-有房，2-无房，3-按揭',
    car_status TINYINT COMMENT '车产情况：1-有车，2-无车',
    
    -- 家庭情况
    parents_occupation VARCHAR(200) COMMENT '父母职业',
    siblings VARCHAR(200) COMMENT '兄弟姐妹情况',
    
    -- 择偶条件
    preferred_age_min INT COMMENT '期望年龄最小值',
    preferred_age_max INT COMMENT '期望年龄最大值',
    preferred_education TINYINT COMMENT '期望学历',
    preferred_height_min INT COMMENT '期望身高最小值',
    preferred_marriage_status TINYINT COMMENT '期望婚姻状况',
    
    -- 其他信息
    hobbies TEXT COMMENT '兴趣爱好',
    values_notes TEXT COMMENT '价值观/备注',
    phone VARCHAR(20) COMMENT '手机号',
    wechat VARCHAR(50) COMMENT '微信号',
    
    -- 系统字段
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-暂停，3-删除',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(50) COMMENT '创建人（红娘账号）',
    
    -- 索引
    INDEX idx_gender_age (gender, age),
    INDEX idx_education_income (education_level, monthly_income),
    INDEX idx_marriage_status (marriage_status),
    INDEX idx_create_time (create_time),
    INDEX idx_creator (creator),
    
    -- 约束
    CONSTRAINT chk_gender CHECK (gender IN (1, 2)),
    CONSTRAINT chk_age CHECK (age BETWEEN 18 AND 80),
    CONSTRAINT chk_height CHECK (height IS NULL OR height BETWEEN 140 AND 220),
    CONSTRAINT chk_weight CHECK (weight IS NULL OR weight BETWEEN 35 AND 200),
    CONSTRAINT chk_education CHECK (education_level IS NULL OR education_level BETWEEN 1 AND 6),
    CONSTRAINT chk_marriage CHECK (marriage_status IN (1, 2, 3))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员信息表';

-- 3. 审计日志表
CREATE TABLE audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    member_id BIGINT NOT NULL COMMENT '会员ID',
    field_name VARCHAR(50) NOT NULL COMMENT '字段名',
    old_value TEXT COMMENT '原值',
    new_value TEXT COMMENT '新值',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-OCR识别，2-规则校验，3-AI纠错，4-人工修改',
    operator VARCHAR(50) NOT NULL COMMENT '操作人',
    operator_type TINYINT NOT NULL COMMENT '操作人类型：1-系统，2-红娘，3-管理员',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_member_id (member_id),
    INDEX idx_field_name (field_name),
    INDEX idx_operation_type (operation_type),
    INDEX idx_create_time (create_time),
    INDEX idx_operator (operator),
    
    FOREIGN KEY (member_id) REFERENCES member(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 4. 系统配置表（用于规则引擎配置）
CREATE TABLE system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL COMMENT '配置类型：string,number,json,boolean',
    description VARCHAR(200) COMMENT '配置描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('validation.age.min', '18', 'number', '年龄最小值'),
('validation.age.max', '80', 'number', '年龄最大值'),
('validation.height.min', '140', 'number', '身高最小值(cm)'),
('validation.height.max', '220', 'number', '身高最大值(cm)'),
('validation.weight.min', '35', 'number', '体重最小值(kg)'),
('validation.weight.max', '200', 'number', '体重最大值(kg)'),
('ocr.confidence.threshold', '0.8', 'number', 'OCR识别置信度阈值'),
('ai.correction.enabled', 'true', 'boolean', '是否启用AI纠错'),
('income.ranges', '[{"value":1,"label":"2000以下"},{"value":2,"label":"2000-4000"},{"value":3,"label":"4000-8000"},{"value":4,"label":"8000-20000"},{"value":5,"label":"20000以上"}]', 'json', '收入区间配置');

-- 5. 用户表（红娘账号管理）
CREATE TABLE user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    role TINYINT NOT NULL DEFAULT 2 COMMENT '角色：1-管理员，2-红娘',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员账号（密码：admin123）
INSERT INTO user (username, password, real_name, role) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiE8qpwUOoxbfTXbe6U7Z3PQBG', '系统管理员', 1);
