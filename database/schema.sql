-- 婚介所后台管理系统数据库设计
-- MySQL 8.0+ 兼容语法

-- 创建数据库
CREATE DATABASE IF NOT EXISTS matchmaking_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE matchmaking_system;

-- 1. 原始OCR数据表
CREATE TABLE raw_ocr_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    member_id BIGINT COMMENT '关联会员ID',
    image_url VARCHAR(500) NOT NULL COMMENT '原始图片URL',
    raw_json TEXT NOT NULL COMMENT 'OCR识别原始JSON结果',
    ocr_confidence DECIMAL(5,2) DEFAULT 0.00 COMMENT 'OCR识别置信度',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_member_id (member_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始OCR识别数据表';

-- 2. 会员信息表
CREATE TABLE member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 基础信息
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender TINYINT NOT NULL COMMENT '性别：1-男，2-女',
    birth_date DATE COMMENT '出生年月',
    age INT NOT NULL COMMENT '年龄',
    ethnicity VARCHAR(20) DEFAULT '汉' COMMENT '民族',
    
    -- 外貌信息
    height INT COMMENT '身高(cm)',
    weight INT COMMENT '体重(kg)',
    
    -- 学历职业
    education_level TINYINT COMMENT '学历：1-高中，2-专科，3-本科，4-硕士，5-博士，6-其他',
    major VARCHAR(100) COMMENT '专业',
    occupation VARCHAR(100) COMMENT '职业',
    company_type TINYINT COMMENT '单位性质：1-国企，2-外企，3-私企，4-事业单位，5-公务员，6-自由职业',
    monthly_income TINYINT COMMENT '月收入：1-2000以下，2-2000-4000，3-4000-8000，4-8000-20000，5-20000以上',
    
    -- 婚姻房产
    marriage_status TINYINT NOT NULL COMMENT '婚姻状况：1-未婚，2-离异，3-丧偶',
    house_status TINYINT COMMENT '房产情况：1-有房，2-无房，3-按揭',
    car_status TINYINT COMMENT '车产情况：1-有车，2-无车',
    
    -- 家庭情况
    parents_occupation VARCHAR(200) COMMENT '父母职业',
    siblings VARCHAR(200) COMMENT '兄弟姐妹情况',
    
    -- 择偶条件
    preferred_age_min INT COMMENT '期望年龄最小值',
    preferred_age_max INT COMMENT '期望年龄最大值',
    preferred_education TINYINT COMMENT '期望学历',
    preferred_height_min INT COMMENT '期望身高最小值',
    preferred_marriage_status TINYINT COMMENT '期望婚姻状况',
    
    -- 其他信息
    hobbies TEXT COMMENT '兴趣爱好',
    values_notes TEXT COMMENT '价值观/备注',
    phone VARCHAR(20) COMMENT '手机号',
    wechat VARCHAR(50) COMMENT '微信号',
    
    -- 系统字段
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-暂停，3-删除',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(50) COMMENT '创建人（红娘账号）',
    
    -- 索引
    INDEX idx_gender_age (gender, age),
    INDEX idx_education_income (education_level, monthly_income),
    INDEX idx_marriage_status (marriage_status),
    INDEX idx_create_time (create_time),
    INDEX idx_creator (creator),
    
    -- 约束
    CONSTRAINT chk_gender CHECK (gender IN (1, 2)),
    CONSTRAINT chk_age CHECK (age BETWEEN 18 AND 80),
    CONSTRAINT chk_height CHECK (height IS NULL OR height BETWEEN 140 AND 220),
    CONSTRAINT chk_weight CHECK (weight IS NULL OR weight BETWEEN 35 AND 200),
    CONSTRAINT chk_education CHECK (education_level IS NULL OR education_level BETWEEN 1 AND 6),
    CONSTRAINT chk_marriage CHECK (marriage_status IN (1, 2, 3))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员信息表';

-- 3. 审计日志表
CREATE TABLE audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    member_id BIGINT NOT NULL COMMENT '会员ID',
    field_name VARCHAR(50) NOT NULL COMMENT '字段名',
    old_value TEXT COMMENT '原值',
    new_value TEXT COMMENT '新值',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-OCR识别，2-规则校验，3-AI纠错，4-人工修改',
    operator VARCHAR(50) NOT NULL COMMENT '操作人',
    operator_type TINYINT NOT NULL COMMENT '操作人类型：1-系统，2-红娘，3-管理员',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_member_id (member_id),
    INDEX idx_field_name (field_name),
    INDEX idx_operation_type (operation_type),
    INDEX idx_create_time (create_time),
    INDEX idx_operator (operator),
    
    FOREIGN KEY (member_id) REFERENCES member(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 4. 系统配置表（用于规则引擎配置）
CREATE TABLE system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL COMMENT '配置类型：string,number,json,boolean',
    description VARCHAR(200) COMMENT '配置描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('validation.age.min', '18', 'number', '年龄最小值'),
('validation.age.max', '80', 'number', '年龄最大值'),
('validation.height.min', '140', 'number', '身高最小值(cm)'),
('validation.height.max', '220', 'number', '身高最大值(cm)'),
('validation.weight.min', '35', 'number', '体重最小值(kg)'),
('validation.weight.max', '200', 'number', '体重最大值(kg)'),
('ocr.confidence.threshold', '0.8', 'number', 'OCR识别置信度阈值'),
('ai.correction.enabled', 'true', 'boolean', '是否启用AI纠错'),
('income.ranges', '[{"value":1,"label":"2000以下"},{"value":2,"label":"2000-4000"},{"value":3,"label":"4000-8000"},{"value":4,"label":"8000-20000"},{"value":5,"label":"20000以上"}]', 'json', '收入区间配置');

-- 5. 用户表（红娘账号管理）
CREATE TABLE user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    role TINYINT NOT NULL DEFAULT 2 COMMENT '角色：1-管理员，2-红娘',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员账号（密码：admin123）
INSERT INTO user (username, password, real_name, role) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiE8qpwUOoxbfTXbe6U7Z3PQBG', '系统管理员', 1),
('hongniangA', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiE8qpwUOoxbfTXbe6U7Z3PQBG', '红娘A', 2);

-- ========== 智能匹配系统扩展表 ==========

-- 6. 会员画像向量表
CREATE TABLE member_embeddings (
    member_id BIGINT NOT NULL COMMENT '会员ID',
    profile_vector JSON NOT NULL COMMENT '用户画像向量(384维)',
    vector_version VARCHAR(20) NOT NULL DEFAULT 'v1.0' COMMENT '向量版本',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (member_id),
    INDEX idx_vector_version (vector_version),
    INDEX idx_update_time (update_time),

    FOREIGN KEY (member_id) REFERENCES member(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员画像向量表';

-- 7. 匹配记录表
CREATE TABLE match_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    member_a_id BIGINT NOT NULL COMMENT '会员A ID',
    member_b_id BIGINT NOT NULL COMMENT '会员B ID',
    matchmaker_id BIGINT DEFAULT NULL COMMENT '红娘ID',
    match_score DECIMAL(5,4) NOT NULL COMMENT '匹配分数(0-1)',
    semantic_similarity DECIMAL(5,4) DEFAULT NULL COMMENT '语义相似度',
    hard_conditions_score DECIMAL(5,4) DEFAULT NULL COMMENT '硬性条件分数',
    activity_score DECIMAL(5,4) DEFAULT NULL COMMENT '活跃度分数',
    reason TEXT COMMENT 'AI生成的推荐理由',
    ice_breakers JSON COMMENT '破冰话术(JSON数组)',
    date_suggestions JSON COMMENT '约会建议(JSON数组)',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-推荐，1-查看，2-回复，3-约见，4-成功，5-失败',
    feedback VARCHAR(500) COMMENT '反馈信息',
    viewed_time TIMESTAMP NULL COMMENT '查看时间',
    replied_time TIMESTAMP NULL COMMENT '回复时间',
    met_time TIMESTAMP NULL COMMENT '约见时间',
    result_time TIMESTAMP NULL COMMENT '结果确认时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_member_pair (member_a_id, member_b_id),
    INDEX idx_member_a (member_a_id),
    INDEX idx_member_b (member_b_id),
    INDEX idx_matchmaker (matchmaker_id),
    INDEX idx_status (status),
    INDEX idx_match_score (match_score),
    INDEX idx_create_time (create_time),

    FOREIGN KEY (member_a_id) REFERENCES member(id) ON DELETE CASCADE,
    FOREIGN KEY (member_b_id) REFERENCES member(id) ON DELETE CASCADE,
    FOREIGN KEY (matchmaker_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配记录表';

-- 8. 会员偏好配置表
CREATE TABLE member_preferences (
    member_id BIGINT NOT NULL COMMENT '会员ID',
    age_min INT DEFAULT 22 COMMENT '最小年龄偏好',
    age_max INT DEFAULT 45 COMMENT '最大年龄偏好',
    height_min INT DEFAULT 155 COMMENT '最小身高偏好(cm)',
    height_max INT DEFAULT 185 COMMENT '最大身高偏好(cm)',
    education_min TINYINT DEFAULT 1 COMMENT '最低学历要求',
    income_min TINYINT DEFAULT 3 COMMENT '最低收入要求',
    preferred_cities JSON COMMENT '偏好城市(JSON数组)',
    deal_breakers JSON COMMENT '不接受的条件(JSON数组)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用偏好过滤：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (member_id),
    INDEX idx_age_range (age_min, age_max),
    INDEX idx_height_range (height_min, height_max),
    INDEX idx_education_min (education_min),
    INDEX idx_income_min (income_min),

    FOREIGN KEY (member_id) REFERENCES member(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员偏好配置表';

-- 插入匹配算法配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('matching.weights.semantic', '0.45', 'number', '语义相似度权重'),
('matching.weights.hard_conditions', '0.25', 'number', '硬性条件权重'),
('matching.weights.activity', '0.15', 'number', '活跃度权重'),
('matching.weights.matchmaker_priority', '0.10', 'number', '红娘优先级权重'),
('matching.weights.freshness', '0.05', 'number', '新用户权重'),
('matching.vector.model', 'm3e-small', 'string', '向量化模型名称'),
('matching.vector.dimension', '384', 'number', '向量维度'),
('matching.candidate.pool_size', '100', 'number', '候选池大小'),
('matching.result.top_k', '10', 'number', '默认返回匹配数量');
