# 红娘门店AI赋能系统 - 技术实施方案

## 一、入门版核心代码实现

### 1.1 数据库设计

```sql
-- 用户基础信息表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    gender INTEGER NOT NULL, -- 1男2女
    age INTEGER NOT NULL,
    city VARCHAR(50) NOT NULL,
    education_level INTEGER, -- 1-6: 高中/专科/本科/硕士/博士/其他
    height INTEGER, -- cm
    monthly_income INTEGER, -- 千元为单位
    marriage_history INTEGER, -- 0未婚1离异2丧偶
    profile_text TEXT, -- 自我介绍
    hobbies TEXT, -- 爱好标签，逗号分隔
    created_at TIMESTAMP DEFAULT NOW(),
    last_active TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- 向量存储表
CREATE TABLE user_embeddings (
    user_id INTEGER PRIMARY KEY REFERENCES users(id),
    profile_vector VECTOR(384), -- bge-small-zh维度
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 匹配记录表
CREATE TABLE match_records (
    id SERIAL PRIMARY KEY,
    user_a INTEGER REFERENCES users(id),
    user_b INTEGER REFERENCES users(id),
    matchmaker_id INTEGER, -- 红娘ID
    score FLOAT,
    reason TEXT, -- AI生成的推荐理由
    ice_breakers TEXT, -- JSON格式的破冰话术
    status INTEGER DEFAULT 0, -- 0推荐1查看2回复3约见4成功5失败
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_a, user_b)
);

-- 用户偏好表（规则配置）
CREATE TABLE user_preferences (
    user_id INTEGER PRIMARY KEY REFERENCES users(id),
    age_min INTEGER DEFAULT 22,
    age_max INTEGER DEFAULT 45,
    height_min INTEGER DEFAULT 155,
    height_max INTEGER DEFAULT 185,
    education_min INTEGER DEFAULT 1,
    income_min INTEGER DEFAULT 3,
    preferred_cities TEXT, -- JSON数组
    deal_breakers TEXT -- JSON数组：不接受的条件
);
```

### 1.2 核心匹配算法

```python
import numpy as np
from sentence_transformers import SentenceTransformer
import sqlite3
from typing import List, Dict, Tuple
import json

class MatchmakingEngine:
    def __init__(self):
        # 加载轻量级中文向量模型
        self.encoder = SentenceTransformer('moka-ai/m3e-small')
        self.db_path = "matchmaking.db"
        
    def compute_user_embedding(self, user_profile: str, hobbies: str) -> np.ndarray:
        """计算用户画像向量"""
        combined_text = f"{user_profile} {hobbies}"
        return self.encoder.encode(combined_text, normalize_embeddings=True)
    
    def rule_filter(self, target_user: Dict, candidates: List[Dict]) -> List[Dict]:
        """硬性条件过滤"""
        filtered = []
        for candidate in candidates:
            # 年龄匹配
            if not (candidate.get('age_min', 0) <= target_user['age'] <= candidate.get('age_max', 99)):
                continue
            if not (target_user.get('age_min', 0) <= candidate['age'] <= target_user.get('age_max', 99)):
                continue
            
            # 城市匹配
            target_cities = json.loads(target_user.get('preferred_cities', '[]'))
            if target_cities and candidate['city'] not in target_cities:
                continue
                
            # 身高匹配（异性约束）
            if target_user['gender'] != candidate['gender']:
                if target_user['gender'] == 2:  # 女性
                    if candidate['height'] < target_user.get('height_min', 165):
                        continue
                
            filtered.append(candidate)
        return filtered
    
    def semantic_similarity_search(self, target_vector: np.ndarray, k: int = 100) -> List[Tuple[int, float]]:
        """语义相似度检索"""
        # 这里简化为遍历所有向量计算相似度
        # 实际可用 FAISS 或 pgvector 优化
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT user_id, profile_vector FROM user_embeddings")
        results = []
        
        for user_id, vector_blob in cursor.fetchall():
            candidate_vector = np.frombuffer(vector_blob, dtype=np.float32)
            similarity = np.dot(target_vector, candidate_vector)
            results.append((user_id, similarity))
        
        conn.close()
        return sorted(results, key=lambda x: x[1], reverse=True)[:k]
    
    def calculate_match_score(self, user_a: Dict, user_b: Dict, semantic_sim: float) -> float:
        """综合评分公式"""
        # 基础权重
        weights = {
            'semantic': 0.45,
            'hard_conditions': 0.25,
            'activity': 0.15,
            'matchmaker_priority': 0.10,
            'freshness': 0.05
        }
        
        # 硬性条件匹配度
        hard_score = 0
        if abs(user_a['age'] - user_b['age']) <= 5:
            hard_score += 0.3
        if user_a['city'] == user_b['city']:
            hard_score += 0.4
        if abs(user_a.get('education_level', 3) - user_b.get('education_level', 3)) <= 1:
            hard_score += 0.3
            
        # 活跃度分（简化）
        activity_score = min(1.0, (user_b.get('last_active_days_ago', 30) / 30))
        
        # 新用户加权
        freshness_score = 1.0 if user_b.get('is_new_user', False) else 0.5
        
        final_score = (
            weights['semantic'] * semantic_sim +
            weights['hard_conditions'] * hard_score +
            weights['activity'] * activity_score +
            weights['freshness'] * freshness_score
        )
        
        return min(1.0, final_score)
    
    def get_matches(self, user_id: int, top_k: int = 10) -> List[Dict]:
        """获取推荐匹配"""
        # 1. 获取目标用户信息
        target_user = self.get_user_by_id(user_id)
        target_vector = self.get_user_embedding(user_id)
        
        # 2. 语义检索候选池
        semantic_candidates = self.semantic_similarity_search(target_vector, k=100)
        
        # 3. 规则过滤
        candidate_users = [self.get_user_by_id(uid) for uid, _ in semantic_candidates]
        filtered_users = self.rule_filter(target_user, candidate_users)
        
        # 4. 综合打分排序
        scored_matches = []
        for candidate in filtered_users:
            # 找到对应的语义相似度
            sem_sim = next((sim for uid, sim in semantic_candidates if uid == candidate['id']), 0.0)
            score = self.calculate_match_score(target_user, candidate, sem_sim)
            scored_matches.append({
                'user': candidate,
                'score': score,
                'semantic_similarity': sem_sim
            })
        
        return sorted(scored_matches, key=lambda x: x['score'], reverse=True)[:top_k]
```

### 1.3 AI话术生成

```python
import openai
import json
from typing import List

class ContentGenerator:
    def __init__(self, api_key: str, base_url: str = None):
        # 支持通义千问、智谱、DeepSeek等国产API
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url or "https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
    def generate_match_reason(self, user_a: Dict, user_b: Dict) -> str:
        """生成匹配推荐理由"""
        prompt = f"""
作为专业红娘，请分析这两位用户的匹配度并给出推荐理由：

用户A：{user_a['name']}，{user_a['age']}岁，{user_a['city']}，{user_a.get('education_level', '本科')}，兴趣：{user_a.get('hobbies', '')}
个人介绍：{user_a.get('profile_text', '')}

用户B：{user_b['name']}，{user_b['age']}岁，{user_b['city']}，{user_b.get('education_level', '本科')}，兴趣：{user_b.get('hobbies', '')}
个人介绍：{user_b.get('profile_text', '')}

请从以下角度分析（150字以内）：
1. 3个互补优势点
2. 2个共同点
3. 1个需要注意的潜在问题
格式：简洁明了，适合红娘向客户介绍
"""
        
        response = self.client.chat.completions.create(
            model="qwen-turbo",  # 或其他经济模型
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.7
        )
        
        return response.choices[0].message.content.strip()
    
    def generate_ice_breakers(self, user_a: Dict, user_b: Dict) -> List[str]:
        """生成破冰开场白"""
        prompt = f"""
请为红娘客户生成3条不同风格的开场白，用于微信私聊：

对方信息：{user_b['age']}岁，{user_b['city']}，兴趣：{user_b.get('hobbies', '')}
对方介绍：{user_b.get('profile_text', '')}

要求：
1. 每条50-80字
2. 三种风格：温和询问型、共同话题型、轻松幽默型
3. 避免太直接的外貌或收入相关表达
4. 自然真诚，不要太刻意

格式：直接输出3条，用换行分隔
"""
        
        response = self.client.chat.completions.create(
            model="qwen-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=300,
            temperature=0.8
        )
        
        ice_breakers = response.choices[0].message.content.strip().split('\n')
        return [line.strip() for line in ice_breakers if line.strip()][:3]
    
    def generate_date_suggestions(self, user_a: Dict, user_b: Dict, city: str) -> List[str]:
        """生成约会建议"""
        prompt = f"""
为这对配对用户推荐3个{city}的约会地点和活动：

用户A兴趣：{user_a.get('hobbies', '')}
用户B兴趣：{user_b.get('hobbies', '')}

要求：
1. 考虑双方兴趣交集
2. 适合初次见面（公共场所）
3. 包含具体地点名称
4. 每条30-50字

格式：地点名称 - 活动描述
"""
        
        response = self.client.chat.completions.create(
            model="qwen-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200
        )
        
        suggestions = response.choices[0].message.content.strip().split('\n')
        return [line.strip() for line in suggestions if line.strip()][:3]
```

## 二、最小可行后台系统

### 2.1 FastAPI后端核心接口

```python
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
import json

app = FastAPI(title="红娘AI匹配系统")

# 请求模型
class MatchRequest(BaseModel):
    user_id: int
    top_k: int = 10

class MatchFeedback(BaseModel):
    match_id: int
    action: str  # 'view', 'reply', 'meet', 'success', 'reject'
    feedback: Optional[str] = None

# 核心接口
@app.post("/api/matches")
async def get_matches(request: MatchRequest):
    """获取匹配推荐"""
    try:
        engine = MatchmakingEngine()
        matches = engine.get_matches(request.user_id, request.top_k)
        
        # 批量生成话术（带缓存）
        generator = ContentGenerator(api_key="your-api-key")
        
        results = []
        for match in matches:
            match_id = save_match_record(request.user_id, match['user']['id'], match['score'])
            
            # 检查缓存
            cached_content = get_cached_content(request.user_id, match['user']['id'])
            if not cached_content:
                reason = generator.generate_match_reason(
                    get_user_by_id(request.user_id), match['user']
                )
                ice_breakers = generator.generate_ice_breakers(
                    get_user_by_id(request.user_id), match['user']
                )
                cached_content = {
                    'reason': reason,
                    'ice_breakers': ice_breakers
                }
                cache_content(request.user_id, match['user']['id'], cached_content)
            
            results.append({
                'match_id': match_id,
                'candidate': match['user'],
                'score': match['score'],
                'reason': cached_content['reason'],
                'ice_breakers': cached_content['ice_breakers']
            })
        
        return {'matches': results}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/feedback")
async def submit_feedback(feedback: MatchFeedback):
    """提交匹配反馈"""
    update_match_status(feedback.match_id, feedback.action, feedback.feedback)
    return {'status': 'success'}

@app.get("/api/stats")
async def get_stats():
    """获取数据看板"""
    stats = calculate_match_stats()
    return stats

# 辅助函数
def save_match_record(user_a: int, user_b: int, score: float) -> int:
    """保存匹配记录"""
    # 数据库操作
    pass

def get_cached_content(user_a: int, user_b: int) -> Optional[Dict]:
    """获取缓存的话术内容"""
    # Redis或数据库缓存查询
    pass

def cache_content(user_a: int, user_b: int, content: Dict):
    """缓存话术内容"""
    # 缓存存储，过期时间30天
    pass
```

### 2.2 简洁管理后台（Vue.js）

```vue
<template>
  <div class="matchmaker-dashboard">
    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <h3>今日推荐</h3>
        <div class="stat-number">{{ stats.today_matches }}</div>
      </div>
      <div class="stat-card">
        <h3>回复率</h3>
        <div class="stat-number">{{ stats.reply_rate }}%</div>
      </div>
      <div class="stat-card">
        <h3>约见率</h3>
        <div class="stat-number">{{ stats.meet_rate }}%</div>
      </div>
    </div>

    <!-- 用户搜索 -->
    <div class="search-section">
      <input v-model="searchUser" placeholder="搜索用户姓名或ID" />
      <button @click="getMatches" :disabled="!searchUser">获取推荐</button>
    </div>

    <!-- 推荐列表 -->
    <div class="matches-list" v-if="matches.length">
      <div class="match-card" v-for="match in matches" :key="match.match_id">
        <div class="candidate-info">
          <h4>{{ match.candidate.name }}（{{ match.candidate.age }}岁）</h4>
          <p>{{ match.candidate.city }} · {{ match.candidate.education_level }}</p>
          <div class="match-score">匹配度：{{ (match.score * 100).toFixed(1) }}%</div>
        </div>
        
        <div class="match-reason">
          <h5>推荐理由</h5>
          <p>{{ match.reason }}</p>
        </div>
        
        <div class="ice-breakers">
          <h5>破冰话术</h5>
          <div class="ice-breaker" v-for="(ice, index) in match.ice_breakers" :key="index">
            <p>{{ ice }}</p>
            <button @click="copyToClipboard(ice)">复制</button>
          </div>
        </div>
        
        <div class="actions">
          <button @click="markAsViewed(match.match_id)" class="btn-view">已查看</button>
          <button @click="markAsReplied(match.match_id)" class="btn-reply">已回复</button>
          <button @click="markAsMet(match.match_id)" class="btn-meet">已约见</button>
          <button @click="markAsRejected(match.match_id)" class="btn-reject">不合适</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MatchmakerDashboard',
  data() {
    return {
      searchUser: '',
      matches: [],
      stats: {
        today_matches: 0,
        reply_rate: 0,
        meet_rate: 0
      }
    }
  },
  mounted() {
    this.loadStats()
  },
  methods: {
    async getMatches() {
      try {
        const response = await fetch('/api/matches', {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({
            user_id: parseInt(this.searchUser),
            top_k: 10
          })
        })
        const data = await response.json()
        this.matches = data.matches
      } catch (error) {
        alert('获取推荐失败：' + error.message)
      }
    },
    
    async submitFeedback(matchId, action) {
      try {
        await fetch('/api/feedback', {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({
            match_id: matchId,
            action: action
          })
        })
        alert('反馈已提交')
      } catch (error) {
        alert('提交失败：' + error.message)
      }
    },
    
    markAsViewed(matchId) { this.submitFeedback(matchId, 'view') },
    markAsReplied(matchId) { this.submitFeedback(matchId, 'reply') },
    markAsMet(matchId) { this.submitFeedback(matchId, 'meet') },
    markAsRejected(matchId) { this.submitFeedback(matchId, 'reject') },
    
    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
      alert('已复制到剪贴板')
    },
    
    async loadStats() {
      try {
        const response = await fetch('/api/stats')
        this.stats = await response.json()
      } catch (error) {
        console.error('加载统计数据失败', error)
      }
    }
  }
}
</script>

<style scoped>
.matchmaker-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #e91e63;
}

.search-section {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.search-section input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.match-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.candidate-info {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.match-score {
  color: #e91e63;
  font-weight: bold;
}

.ice-breakers .ice-breaker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 10px;
}

.actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-view { background: #17a2b8; color: white; }
.btn-reply { background: #28a745; color: white; }
.btn-meet { background: #ffc107; color: black; }
.btn-reject { background: #dc3545; color: white; }
</style>
```

## 三、成本控制与部署建议

### 3.1 服务器配置（入门版）

**推荐配置**：
- **云服务器**：2C4G，系统盘50GB（阿里云/腾讯云约200-300元/月）
- **数据库**：PostgreSQL 云数据库基础版（约100-200元/月）
- **API调用**：LLM按量付费（预计50-150元/月，根据用户量）

**部署架构**：
```
用户 → Nginx → FastAPI → PostgreSQL
                ↓
            Redis缓存 → LLM API
```

### 3.2 关键优化点

1. **Embedding计算优化**
   - 批量计算用户向量，避免实时计算
   - 使用CPU版本的sentence-transformers
   - 向量维度选择384（m3e-small）平衡效果与成本

2. **LLM调用优化**
   - 严格控制prompt长度和输出token数
   - 相同用户对的话术缓存7-30天
   - 使用模板+填空减少API调用

3. **数据库查询优化**
   - 为常用查询字段建索引
   - 使用LIMIT限制查询结果
   - 考虑读写分离（数据量大时）

### 3.3 扩容路径

**阶段1**（0-1000用户）：单机部署，SQLite/PostgreSQL
**阶段2**（1000-10000用户）：数据库分离，引入Redis，考虑向量数据库
**阶段3**（10000+用户）：微服务拆分，引入消息队列，AI模型本地化

## 四、监控与运营

### 4.1 关键指标监控

```python
# 每日统计脚本
def daily_metrics():
    metrics = {
        'matches_generated': count_matches_today(),
        'view_rate': calculate_view_rate(),
        'reply_rate': calculate_reply_rate(),
        'meet_rate': calculate_meet_rate(),
        'success_rate': calculate_success_rate(),
        'avg_match_score': calculate_avg_score(),
        'api_cost': calculate_daily_api_cost()
    }
    
    # 发送到监控系统或邮件
    send_daily_report(metrics)
    
    # 异常告警
    if metrics['reply_rate'] < 20:
        alert("回复率异常偏低")
    if metrics['api_cost'] > daily_budget * 1.2:
        alert("API成本超预算")
```

### 4.2 A/B实验框架

```python
def ab_test_score_formula():
    """测试不同权重配置"""
    test_groups = {
        'control': {'semantic': 0.45, 'hard': 0.25, 'activity': 0.15},
        'experiment': {'semantic': 0.35, 'hard': 0.35, 'activity': 0.20}
    }
    
    # 随机分组用户
    # 追踪各组的回复率、约见率差异
```

这个方案的核心是"先跑起来，再优化"，重点关注：

1. **极简MVP**：核心功能优先，避免过度设计
2. **成本透明**：每个组件的费用可预估可控制
3. **数据驱动**：从第一天就建立指标体系
4. **渐进升级**：架构支持平滑扩容

你觉得这个实施方案如何？需要我详细展开某个具体模块吗？