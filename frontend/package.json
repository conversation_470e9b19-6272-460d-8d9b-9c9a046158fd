{"name": "matchmaking-frontend", "version": "1.0.0", "description": "婚介所后台管理系统前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "ant-design-vue": "^4.0.0", "axios": "^1.5.0", "@ant-design/icons-vue": "^7.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.3"}}