<template>
  <div id="app">
    <a-layout style="min-height: 100vh">
      <!-- 头部 -->
      <a-layout-header style="background: #fff; padding: 0 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)">
        <div style="display: flex; align-items: center; height: 64px">
          <h1 style="margin: 0; color: #1890ff; font-size: 20px">
            婚介所后台管理系统
          </h1>
          <div style="margin-left: auto">
            <span style="color: #666">第一阶段：表格识别录入</span>
          </div>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content style="padding: 24px">
        <router-view />
      </a-layout-content>

      <!-- 底部 -->
      <a-layout-footer style="text-align: center; background: #f0f2f5">
        婚介所后台管理系统 ©2024 - AI赋能数据录入
      </a-layout-footer>
    </a-layout>

    <!-- 全局加载遮罩 -->
    <div v-if="globalLoading" class="loading-overlay">
      <a-spin size="large" tip="处理中，请稍候...">
        <div style="width: 200px; height: 100px"></div>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useGlobalStore } from '@/stores/global'

const globalStore = useGlobalStore()
const globalLoading = ref(false)

// 监听全局加载状态
globalStore.$subscribe((mutation, state) => {
  globalLoading.value = state.loading
})
</script>

<style scoped>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}
</style>
