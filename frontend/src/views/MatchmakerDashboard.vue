<template>
  <div class="matchmaker-dashboard">
    <!-- 顶部统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日推荐"
              :value="todayStats.totalMatches"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="查看率"
              :value="todayStats.viewRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="回复率"
              :value="todayStats.replyRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="约见率"
              :value="todayStats.meetRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#f5222d' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索区域 -->
    <a-card class="search-section" title="智能匹配推荐">
      <a-form layout="inline" :model="searchForm" @finish="getMatches">
        <a-form-item label="会员ID" name="memberId">
          <a-input-number
            v-model:value="searchForm.memberId"
            placeholder="请输入会员ID"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="推荐数量" name="topK">
          <a-select v-model:value="searchForm.topK" style="width: 120px">
            <a-select-option :value="5">5个</a-select-option>
            <a-select-option :value="10">10个</a-select-option>
            <a-select-option :value="20">20个</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            获取推荐
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 匹配结果列表 -->
    <div class="matches-section" v-if="matches.length > 0">
      <a-card
        v-for="match in matches"
        :key="match.matchId || match.candidate.id"
        class="match-card"
        :title="`${match.candidate.name}（${match.candidate.age}岁）`"
      >
        <template #extra>
          <a-tag :color="getScoreColor(match.matchScore)">
            匹配度：{{ (match.matchScore * 100).toFixed(1) }}%
          </a-tag>
        </template>

        <a-row :gutter="16">
          <a-col :span="8">
            <div class="candidate-info">
              <p><strong>基本信息：</strong></p>
              <p>性别：{{ match.candidate.gender === 1 ? '男' : '女' }}</p>
              <p>城市：{{ match.candidate.city }}</p>
              <p>身高：{{ match.candidate.height }}cm</p>
              <p>学历：{{ getEducationLabel(match.candidate.educationLevel) }}</p>
              <p>收入：{{ getIncomeLabel(match.candidate.monthlyIncome) }}</p>
            </div>
          </a-col>
          
          <a-col :span="10">
            <div class="match-content">
              <div class="match-reason" v-if="match.reason">
                <h4>推荐理由：</h4>
                <p>{{ match.reason }}</p>
              </div>
              
              <div class="ice-breakers" v-if="match.iceBreakers && match.iceBreakers.length > 0">
                <h4>破冰话术：</h4>
                <div
                  v-for="(ice, index) in match.iceBreakers"
                  :key="index"
                  class="ice-breaker-item"
                >
                  <p>{{ ice }}</p>
                  <a-button size="small" @click="copyToClipboard(ice)">
                    复制
                  </a-button>
                </div>
              </div>
            </div>
          </a-col>
          
          <a-col :span="6">
            <div class="actions">
              <a-space direction="vertical" style="width: 100%">
                <a-button
                  type="default"
                  block
                  @click="submitFeedback(match.matchId, 'view')"
                  :disabled="match.status >= 1"
                >
                  {{ match.status >= 1 ? '已查看' : '标记查看' }}
                </a-button>
                <a-button
                  type="primary"
                  block
                  @click="submitFeedback(match.matchId, 'reply')"
                  :disabled="match.status >= 2"
                >
                  {{ match.status >= 2 ? '已回复' : '标记回复' }}
                </a-button>
                <a-button
                  type="success"
                  block
                  @click="submitFeedback(match.matchId, 'meet')"
                  :disabled="match.status >= 3"
                >
                  {{ match.status >= 3 ? '已约见' : '标记约见' }}
                </a-button>
                <a-button
                  type="danger"
                  block
                  @click="submitFeedback(match.matchId, 'reject')"
                  :disabled="match.status >= 4"
                >
                  不合适
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 空状态 -->
    <a-empty v-if="!loading && matches.length === 0" description="暂无匹配推荐" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import matchingApi from '@/api/matching'

// 响应式数据
const loading = ref(false)
const matches = ref([])
const todayStats = ref({
  totalMatches: 0,
  viewRate: 0,
  replyRate: 0,
  meetRate: 0
})

const searchForm = reactive({
  memberId: null,
  topK: 10,
  matchmakerId: 1 // 当前红娘ID，实际应该从用户信息获取
})

// 生命周期
onMounted(() => {
  loadTodayStats()
})

// 方法
const getMatches = async () => {
  if (!searchForm.memberId) {
    message.error('请输入会员ID')
    return
  }

  loading.value = true
  try {
    const response = await matchingApi.getMatches({
      memberId: searchForm.memberId,
      topK: searchForm.topK,
      matchmakerId: searchForm.matchmakerId,
      includeAiContent: true
    })
    
    matches.value = response.data || []
    message.success(`获取到 ${matches.value.length} 个匹配推荐`)
  } catch (error) {
    message.error('获取匹配推荐失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const submitFeedback = async (matchId, action) => {
  if (!matchId) {
    message.error('匹配记录ID不存在')
    return
  }

  try {
    await matchingApi.submitFeedback({
      matchId,
      action,
      operatorId: searchForm.matchmakerId
    })
    
    message.success('反馈提交成功')
    
    // 更新本地状态
    const match = matches.value.find(m => m.matchId === matchId)
    if (match) {
      const statusMap = { view: 1, reply: 2, meet: 3, success: 4, reject: 5 }
      match.status = statusMap[action] || match.status
    }
    
    // 刷新统计数据
    loadTodayStats()
  } catch (error) {
    message.error('提交反馈失败：' + error.message)
  }
}

const loadTodayStats = async () => {
  try {
    const response = await matchingApi.getTodayStatistics(searchForm.matchmakerId)
    todayStats.value = response.data || {}
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const getScoreColor = (score) => {
  if (score >= 0.8) return 'green'
  if (score >= 0.6) return 'orange'
  return 'red'
}

const getEducationLabel = (level) => {
  const labels = {
    1: '高中',
    2: '专科',
    3: '本科',
    4: '硕士',
    5: '博士',
    6: '其他'
  }
  return labels[level] || '未知'
}

const getIncomeLabel = (income) => {
  const labels = {
    1: '2000以下',
    2: '2000-4000',
    3: '4000-8000',
    4: '8000-20000',
    5: '20000以上'
  }
  return labels[income] || '未知'
}
</script>

<style scoped>
.matchmaker-dashboard {
  padding: 24px;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.search-section {
  margin-bottom: 24px;
}

.matches-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.match-card {
  margin-bottom: 16px;
}

.candidate-info p {
  margin: 4px 0;
}

.match-reason {
  margin-bottom: 16px;
}

.match-reason h4 {
  margin-bottom: 8px;
  color: #1890ff;
}

.ice-breakers h4 {
  margin-bottom: 8px;
  color: #52c41a;
}

.ice-breaker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 8px;
}

.ice-breaker-item p {
  margin: 0;
  flex: 1;
}

.actions {
  padding: 16px;
}
</style>
