<template>
  <div class="form-upload-page">
    <!-- 步骤指示器 -->
    <a-steps :current="currentStep" style="margin-bottom: 32px">
      <a-step title="上传表格" description="上传会员登记表格图片" />
      <a-step title="OCR识别" description="AI自动识别表格内容" />
      <a-step title="数据校验" description="规则引擎校验数据合法性" />
      <a-step title="人工确认" description="红娘确认修改数据" />
      <a-step title="保存入库" description="数据保存到数据库" />
    </a-steps>

    <!-- 第一步：文件上传 -->
    <div v-if="currentStep === 0" class="upload-container">
      <h2 style="margin-bottom: 24px">上传会员登记表格</h2>
      
      <!-- 操作人输入 -->
      <div style="margin-bottom: 24px">
        <label class="field-label">操作人（红娘账号）：</label>
        <a-input 
          v-model:value="operator" 
          placeholder="请输入红娘账号"
          style="width: 200px"
        />
      </div>

      <!-- 文件上传 -->
      <a-upload-dragger
        v-model:fileList="fileList"
        :before-upload="beforeUpload"
        :custom-request="handleUpload"
        :show-upload-list="false"
        accept=".jpg,.jpeg,.png,.pdf"
        :multiple="false"
      >
        <p class="ant-upload-drag-icon">
          <inbox-outlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持JPG、PNG、PDF格式，文件大小不超过10MB
        </p>
      </a-upload-dragger>

      <!-- 上传的文件预览 -->
      <div v-if="uploadedFile" style="margin-top: 24px">
        <a-card title="已上传文件" size="small">
          <p><strong>文件名：</strong>{{ uploadedFile.name }}</p>
          <p><strong>文件大小：</strong>{{ formatFileSize(uploadedFile.size) }}</p>
          <p><strong>文件类型：</strong>{{ uploadedFile.type }}</p>
        </a-card>
      </div>
    </div>

    <!-- 第二步到第四步：OCR结果和表单编辑 -->
    <div v-if="currentStep >= 1 && ocrResult" class="form-container">
      <h2 style="margin-bottom: 24px">
        {{ currentStep === 1 ? 'OCR识别结果' : 
           currentStep === 2 ? '数据校验结果' : 
           currentStep === 3 ? '人工确认修改' : '数据保存' }}
      </h2>

      <!-- OCR识别信息 -->
      <div v-if="currentStep === 1" style="margin-bottom: 24px">
        <a-alert
          :message="`OCR识别完成，置信度：${(ocrResult.confidence * 100).toFixed(1)}%`"
          type="success"
          show-icon
          style="margin-bottom: 16px"
        />
        <a-button type="primary" @click="nextStep">下一步：数据校验</a-button>
      </div>

      <!-- 校验错误信息 -->
      <div v-if="currentStep === 2 && validationErrors.length > 0" style="margin-bottom: 24px">
        <a-alert
          :message="`发现 ${validationErrors.length} 个数据错误，请查看下方标红字段`"
          type="warning"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <!-- AI纠错建议 -->
        <div v-if="ocrResult.aiCorrections && Object.keys(ocrResult.aiCorrections).length > 0">
          <h4 style="margin-bottom: 12px">AI纠错建议：</h4>
          <div v-for="(value, field) in ocrResult.aiCorrections" :key="field" class="ai-suggestion">
            <strong>{{ getFieldLabel(field) }}：</strong>
            建议修改为 "{{ value }}"
            <a-button 
              type="link" 
              size="small" 
              @click="applyAiSuggestion(field, value)"
            >
              采纳建议
            </a-button>
          </div>
        </div>
        
        <div style="margin-top: 16px">
          <a-button type="primary" @click="nextStep">下一步：人工确认</a-button>
        </div>
      </div>

      <div v-if="currentStep === 2 && validationErrors.length === 0" style="margin-bottom: 24px">
        <a-alert
          message="数据校验通过，所有字段均符合规则要求"
          type="success"
          show-icon
          style="margin-bottom: 16px"
        />
        <a-button type="primary" @click="nextStep">下一步：人工确认</a-button>
      </div>

      <!-- 表单编辑区域 -->
      <div class="member-form">
        <h3>会员基本信息</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="form-item" :class="{ 'required-field': true, 'error-field': hasError('name') }">
              <label class="field-label">姓名</label>
              <a-input
                v-model:value="formData.name"
                placeholder="请输入姓名"
                :readonly="currentStep < 3"
                @change="handleFieldChange('name', $event.target.value)"
              />
              <div v-if="hasError('name')" class="error-message">
                {{ getErrorMessage('name') }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="form-item" :class="{ 'required-field': true, 'error-field': hasError('gender') }">
              <label class="field-label">性别</label>
              <a-select
                v-model:value="formData.gender"
                placeholder="请选择性别"
                :disabled="currentStep < 3"
                @change="handleFieldChange('gender', $event)"
              >
                <a-select-option :value="1">男</a-select-option>
                <a-select-option :value="2">女</a-select-option>
              </a-select>
              <div v-if="hasError('gender')" class="error-message">
                {{ getErrorMessage('gender') }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="form-item" :class="{ 'required-field': true, 'error-field': hasError('age') }">
              <label class="field-label">年龄</label>
              <a-input-number
                v-model:value="formData.age"
                placeholder="请输入年龄"
                :min="18"
                :max="80"
                :readonly="currentStep < 3"
                @change="handleFieldChange('age', $event)"
                style="width: 100%"
              />
              <div v-if="hasError('age')" class="error-message">
                {{ getErrorMessage('age') }}
              </div>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="8">
            <div class="form-item" :class="{ 'error-field': hasError('height') }">
              <label class="field-label">身高(cm)</label>
              <a-input-number
                v-model:value="formData.height"
                placeholder="请输入身高"
                :min="140"
                :max="220"
                :readonly="currentStep < 3"
                @change="handleFieldChange('height', $event)"
                style="width: 100%"
              />
              <div v-if="hasError('height')" class="error-message">
                {{ getErrorMessage('height') }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="form-item" :class="{ 'error-field': hasError('educationLevel') }">
              <label class="field-label">学历</label>
              <a-select
                v-model:value="formData.educationLevel"
                placeholder="请选择学历"
                :disabled="currentStep < 3"
                @change="handleFieldChange('educationLevel', $event)"
              >
                <a-select-option :value="1">高中</a-select-option>
                <a-select-option :value="2">专科</a-select-option>
                <a-select-option :value="3">本科</a-select-option>
                <a-select-option :value="4">硕士</a-select-option>
                <a-select-option :value="5">博士</a-select-option>
                <a-select-option :value="6">其他</a-select-option>
              </a-select>
              <div v-if="hasError('educationLevel')" class="error-message">
                {{ getErrorMessage('educationLevel') }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="form-item" :class="{ 'required-field': true, 'error-field': hasError('marriageStatus') }">
              <label class="field-label">婚姻状况</label>
              <a-select
                v-model:value="formData.marriageStatus"
                placeholder="请选择婚姻状况"
                :disabled="currentStep < 3"
                @change="handleFieldChange('marriageStatus', $event)"
              >
                <a-select-option :value="1">未婚</a-select-option>
                <a-select-option :value="2">离异</a-select-option>
                <a-select-option :value="3">丧偶</a-select-option>
              </a-select>
              <div v-if="hasError('marriageStatus')" class="error-message">
                {{ getErrorMessage('marriageStatus') }}
              </div>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <div class="form-item" :class="{ 'error-field': hasError('phone') }">
              <label class="field-label">手机号</label>
              <a-input
                v-model:value="formData.phone"
                placeholder="请输入手机号"
                :readonly="currentStep < 3"
                @change="handleFieldChange('phone', $event.target.value)"
              />
              <div v-if="hasError('phone')" class="error-message">
                {{ getErrorMessage('phone') }}
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="form-item">
              <label class="field-label">微信号</label>
              <a-input
                v-model:value="formData.wechat"
                placeholder="请输入微信号"
                :readonly="currentStep < 3"
                @change="handleFieldChange('wechat', $event.target.value)"
              />
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮 -->
      <div v-if="currentStep === 3" class="action-buttons">
        <a-space>
          <a-button @click="prevStep">上一步</a-button>
          <a-button type="primary" @click="confirmSave" :loading="saving">
            确认保存
          </a-button>
          <a-button @click="resetForm">重新开始</a-button>
        </a-space>
      </div>
    </div>

    <!-- 第五步：保存成功 -->
    <div v-if="currentStep === 4" class="form-container">
      <a-result
        status="success"
        title="会员信息保存成功！"
        :sub-title="`会员ID：${savedMember?.id}，姓名：${savedMember?.name}`"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="resetForm">录入下一个</a-button>
            <a-button @click="viewMember">查看详情</a-button>
          </a-space>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'
import { memberApi } from '@/api/member'
import { useGlobalStore } from '@/stores/global'
// import MemberForm from '@/components/MemberForm.vue'

const globalStore = useGlobalStore()

// 响应式数据
const currentStep = ref(0)
const operator = ref('system')
const fileList = ref([])
const uploadedFile = ref(null)
const ocrResult = ref(null)
const formData = ref({})
const saving = ref(false)
const savedMember = ref(null)

// 计算属性
const validationErrors = computed(() => {
  return ocrResult.value?.validationErrors || []
})

// 生命周期
onMounted(() => {
  // 检查服务健康状态
  checkHealth()
})

// 方法
const checkHealth = async () => {
  try {
    await memberApi.health()
    console.log('后端服务连接正常')
  } catch (error) {
    message.error('后端服务连接失败，请检查服务是否启动')
  }
}

const beforeUpload = (file) => {
  // 文件类型检查
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    message.error('只支持JPG、PNG、PDF格式的文件')
    return false
  }

  // 文件大小检查
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB')
    return false
  }

  return false // 阻止自动上传
}

const handleUpload = async ({ file }) => {
  try {
    uploadedFile.value = file
    globalStore.setLoading(true)
    
    // 调用OCR识别API
    const result = await memberApi.uploadForm(file, operator.value)
    
    ocrResult.value = result.data
    formData.value = { ...result.data.fields }
    
    // 自动进入下一步
    currentStep.value = 1
    
    message.success('文件上传成功，OCR识别完成')
    
    // 自动进入校验步骤
    setTimeout(() => {
      nextStep()
    }, 2000)
    
  } catch (error) {
    console.error('上传失败:', error)
    message.error('上传失败，请重试')
  } finally {
    globalStore.setLoading(false)
  }
}

const nextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const applyAiSuggestion = (field, value) => {
  formData.value[field] = value
  message.success(`已采纳AI建议：${getFieldLabel(field)} = ${value}`)
}

const handleFieldChange = (field, value) => {
  formData.value[field] = value
}

const confirmSave = async () => {
  try {
    saving.value = true
    
    // 保存会员信息
    const result = await memberApi.confirmAndSave(
      formData.value,
      ocrResult.value.rawOcrDataId,
      operator.value
    )
    
    savedMember.value = result.data
    currentStep.value = 4
    
    message.success('会员信息保存成功')
    
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  currentStep.value = 0
  fileList.value = []
  uploadedFile.value = null
  ocrResult.value = null
  formData.value = {}
  savedMember.value = null
  operator.value = 'system'
}

const viewMember = () => {
  if (savedMember.value) {
    // 这里可以跳转到会员详情页面
    message.info('会员详情页面开发中...')
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFieldLabel = (field) => {
  const labels = {
    name: '姓名',
    gender: '性别',
    age: '年龄',
    birthDate: '出生日期',
    height: '身高',
    weight: '体重',
    educationLevel: '学历',
    marriageStatus: '婚姻状况',
    monthlyIncome: '月收入',
    phone: '手机号'
    // 可以添加更多字段标签
  }
  return labels[field] || field
}

const hasError = (fieldName) => {
  return validationErrors.value.some(error => error.fieldName === fieldName)
}

const getErrorMessage = (fieldName) => {
  const error = validationErrors.value.find(error => error.fieldName === fieldName)
  return error ? error.errorMessage : ''
}
</script>

<style scoped>
.form-upload-page {
  max-width: 1200px;
  margin: 0 auto;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}
</style>
