<template>
  <div class="content-generator">
    <a-card title="AI内容生成工具" class="main-card">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 匹配推荐 -->
        <a-tab-pane key="match" tab="匹配推荐">
          <a-form layout="vertical" class="match-form">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="会员A ID">
                  <a-input v-model:value="matchForm.memberAId" placeholder="请输入会员A的ID" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="会员B ID">
                  <a-input v-model:value="matchForm.memberBId" placeholder="请输入会员B的ID" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="generateMatchReason" :loading="loading.matchReason">
                  生成推荐理由
                </a-button>
                <a-button @click="generateIceBreakers" :loading="loading.iceBreakers">
                  生成开场白
                </a-button>
                <a-button @click="generateDateSuggestions" :loading="loading.dateSuggestions">
                  生成约会建议
                </a-button>
                <a-button type="dashed" @click="generateAllContent" :loading="loading.batchGenerate">
                  一键生成全部
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <!-- 结果展示 -->
          <div class="results-section" v-if="hasResults">
            <!-- 推荐理由 -->
            <a-card v-if="results.matchReason" title="匹配推荐理由" size="small" class="result-card">
              <p>{{ results.matchReason }}</p>
              <a-button size="small" @click="copyToClipboard(results.matchReason)">复制</a-button>
            </a-card>

            <!-- 开场白 -->
            <a-card v-if="results.iceBreakers && results.iceBreakers.length" title="破冰开场白" size="small" class="result-card">
              <div v-for="(iceBreaker, index) in results.iceBreakers" :key="index" class="ice-breaker-item">
                <a-tag :color="getTagColor(index)">{{ getStyleName(index) }}</a-tag>
                <p>{{ iceBreaker }}</p>
                <a-button size="small" @click="copyToClipboard(iceBreaker)">复制</a-button>
              </div>
            </a-card>

            <!-- 约会建议 -->
            <a-card v-if="results.dateSuggestions && results.dateSuggestions.length" title="约会建议" size="small" class="result-card">
              <div v-for="(suggestion, index) in results.dateSuggestions" :key="index" class="date-suggestion-item">
                <a-icon type="environment" />
                <span>{{ suggestion }}</span>
                <a-button size="small" @click="copyToClipboard(suggestion)">复制</a-button>
              </div>
            </a-card>
          </div>
        </a-tab-pane>

        <!-- 个人介绍优化 -->
        <a-tab-pane key="profile" tab="个人介绍优化">
          <a-form layout="vertical" class="profile-form">
            <a-form-item label="会员ID">
              <a-input v-model:value="profileForm.memberId" placeholder="请输入会员ID" />
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" @click="generateProfileOptimization" :loading="loading.profileOptimization">
                生成优化建议
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 优化建议结果 -->
          <a-card v-if="results.profileOptimization" title="个人介绍优化建议" size="small" class="result-card">
            <pre class="optimization-text">{{ results.profileOptimization }}</pre>
            <a-button size="small" @click="copyToClipboard(results.profileOptimization)">复制</a-button>
          </a-card>
        </a-tab-pane>

        <!-- 服务状态 -->
        <a-tab-pane key="status" tab="服务状态">
          <a-card title="AI服务状态" size="small">
            <a-descriptions :column="2">
              <a-descriptions-item label="服务状态">
                <a-badge :status="serviceStatus.available ? 'success' : 'error'" 
                         :text="serviceStatus.available ? '正常' : '异常'" />
              </a-descriptions-item>
              <a-descriptions-item label="最后检查时间">
                {{ serviceStatus.lastCheck ? new Date(serviceStatus.lastCheck).toLocaleString() : '未检查' }}
              </a-descriptions-item>
            </a-descriptions>
            
            <a-button @click="checkServiceHealth" :loading="loading.healthCheck">
              检查服务状态
            </a-button>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { contentApi } from '@/api/content'

export default {
  name: 'ContentGenerator',
  setup() {
    const activeTab = ref('match')
    
    // 表单数据
    const matchForm = reactive({
      memberAId: '',
      memberBId: '',
      city: ''
    })
    
    const profileForm = reactive({
      memberId: ''
    })
    
    // 加载状态
    const loading = reactive({
      matchReason: false,
      iceBreakers: false,
      dateSuggestions: false,
      batchGenerate: false,
      profileOptimization: false,
      healthCheck: false
    })
    
    // 结果数据
    const results = reactive({
      matchReason: '',
      iceBreakers: [],
      dateSuggestions: [],
      profileOptimization: ''
    })
    
    // 服务状态
    const serviceStatus = reactive({
      available: false,
      lastCheck: null
    })
    
    // 计算属性
    const hasResults = computed(() => {
      return results.matchReason || 
             (results.iceBreakers && results.iceBreakers.length > 0) ||
             (results.dateSuggestions && results.dateSuggestions.length > 0)
    })
    
    // 生成匹配推荐理由
    const generateMatchReason = async () => {
      if (!matchForm.memberAId || !matchForm.memberBId) {
        message.warning('请输入会员A和会员B的ID')
        return
      }
      
      loading.matchReason = true
      try {
        const response = await contentApi.generateMatchReason({
          memberAId: parseInt(matchForm.memberAId),
          memberBId: parseInt(matchForm.memberBId)
        })
        
        results.matchReason = response.data
        message.success('推荐理由生成成功')
      } catch (error) {
        message.error('生成推荐理由失败: ' + error.message)
      } finally {
        loading.matchReason = false
      }
    }
    
    // 生成破冰开场白
    const generateIceBreakers = async () => {
      if (!matchForm.memberAId || !matchForm.memberBId) {
        message.warning('请输入会员A和会员B的ID')
        return
      }
      
      loading.iceBreakers = true
      try {
        const response = await contentApi.generateIceBreakers({
          memberAId: parseInt(matchForm.memberAId),
          memberBId: parseInt(matchForm.memberBId)
        })
        
        results.iceBreakers = response.data
        message.success('开场白生成成功')
      } catch (error) {
        message.error('生成开场白失败: ' + error.message)
      } finally {
        loading.iceBreakers = false
      }
    }
    
    // 生成约会建议
    const generateDateSuggestions = async () => {
      if (!matchForm.memberAId || !matchForm.memberBId) {
        message.warning('请输入会员A和会员B的ID')
        return
      }
      
      loading.dateSuggestions = true
      try {
        const response = await contentApi.generateDateSuggestions({
          memberAId: parseInt(matchForm.memberAId),
          memberBId: parseInt(matchForm.memberBId),
          city: matchForm.city
        })
        
        results.dateSuggestions = response.data
        message.success('约会建议生成成功')
      } catch (error) {
        message.error('生成约会建议失败: ' + error.message)
      } finally {
        loading.dateSuggestions = false
      }
    }
    
    // 一键生成全部内容
    const generateAllContent = async () => {
      if (!matchForm.memberAId || !matchForm.memberBId) {
        message.warning('请输入会员A和会员B的ID')
        return
      }
      
      loading.batchGenerate = true
      try {
        const response = await contentApi.generateMatchContentBatch({
          memberAId: parseInt(matchForm.memberAId),
          memberBId: parseInt(matchForm.memberBId),
          city: matchForm.city,
          includeReason: true,
          includeIceBreakers: true,
          includeDateSuggestions: true
        })
        
        const data = response.data
        results.matchReason = data.matchReason || ''
        results.iceBreakers = data.iceBreakers || []
        results.dateSuggestions = data.dateSuggestions || []
        
        message.success('全部内容生成成功')
      } catch (error) {
        message.error('批量生成失败: ' + error.message)
      } finally {
        loading.batchGenerate = false
      }
    }
    
    // 生成个人介绍优化建议
    const generateProfileOptimization = async () => {
      if (!profileForm.memberId) {
        message.warning('请输入会员ID')
        return
      }
      
      loading.profileOptimization = true
      try {
        const response = await contentApi.generateProfileOptimization({
          memberId: parseInt(profileForm.memberId)
        })
        
        results.profileOptimization = response.data
        message.success('优化建议生成成功')
      } catch (error) {
        message.error('生成优化建议失败: ' + error.message)
      } finally {
        loading.profileOptimization = false
      }
    }
    
    // 检查服务健康状态
    const checkServiceHealth = async () => {
      loading.healthCheck = true
      try {
        const response = await contentApi.checkHealth()
        serviceStatus.available = response.data.available
        serviceStatus.lastCheck = response.data.timestamp
        
        message.success('服务状态检查完成')
      } catch (error) {
        serviceStatus.available = false
        serviceStatus.lastCheck = Date.now()
        message.error('服务状态检查失败: ' + error.message)
      } finally {
        loading.healthCheck = false
      }
    }
    
    // 复制到剪贴板
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        message.success('已复制到剪贴板')
      } catch (error) {
        message.error('复制失败')
      }
    }
    
    // 获取标签颜色
    const getTagColor = (index) => {
      const colors = ['blue', 'green', 'orange']
      return colors[index % colors.length]
    }
    
    // 获取风格名称
    const getStyleName = (index) => {
      const styles = ['温和询问型', '共同话题型', '轻松幽默型']
      return styles[index] || `风格${index + 1}`
    }
    
    return {
      activeTab,
      matchForm,
      profileForm,
      loading,
      results,
      serviceStatus,
      hasResults,
      generateMatchReason,
      generateIceBreakers,
      generateDateSuggestions,
      generateAllContent,
      generateProfileOptimization,
      checkServiceHealth,
      copyToClipboard,
      getTagColor,
      getStyleName
    }
  }
}
</script>

<style scoped>
.content-generator {
  padding: 20px;
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
}

.match-form, .profile-form {
  margin-bottom: 20px;
}

.results-section {
  margin-top: 30px;
}

.result-card {
  margin-bottom: 20px;
}

.ice-breaker-item {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.ice-breaker-item p {
  margin: 8px 0;
  line-height: 1.6;
}

.date-suggestion-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.date-suggestion-item span {
  flex: 1;
  margin: 0 10px;
}

.optimization-text {
  white-space: pre-wrap;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 10px;
}
</style>
