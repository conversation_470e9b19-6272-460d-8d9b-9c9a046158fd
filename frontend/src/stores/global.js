import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useGlobalStore = defineStore('global', () => {
  // 全局加载状态
  const loading = ref(false)
  
  // 当前处理的OCR结果
  const currentOcrResult = ref(null)
  
  // 当前操作人
  const currentOperator = ref('system')

  // 设置加载状态
  const setLoading = (status) => {
    loading.value = status
  }

  // 设置OCR结果
  const setOcrResult = (result) => {
    currentOcrResult.value = result
  }

  // 设置操作人
  const setOperator = (operator) => {
    currentOperator.value = operator
  }

  // 清空数据
  const clearData = () => {
    currentOcrResult.value = null
    loading.value = false
  }

  return {
    loading,
    currentOcrResult,
    currentOperator,
    setLoading,
    setOcrResult,
    setOperator,
    clearData
  }
})
