import { createRouter, createWebHistory } from 'vue-router'
import FormUpload from '@/views/FormUpload.vue'

const routes = [
  {
    path: '/',
    redirect: '/upload'
  },
  {
    path: '/upload',
    name: 'FormUpload',
    component: FormUpload,
    meta: {
      title: '表格上传识别'
    }
  },
  {
    path: '/content-generator',
    name: 'ContentGenerator',
    component: () => import('@/views/ContentGenerator.vue'),
    meta: {
      title: 'AI内容生成'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 婚介所后台管理系统`
  }
  next()
})

export default router
