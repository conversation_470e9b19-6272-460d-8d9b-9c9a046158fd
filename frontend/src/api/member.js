import axios from 'axios'
import { message } from 'ant-design-vue'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    
    // 统一处理响应
    if (data.code === 200) {
      return data
    } else {
      message.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  error => {
    console.error('API请求错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message.error(data.message || '请求参数错误')
          break
        case 401:
          message.error('未授权访问')
          break
        case 403:
          message.error('禁止访问')
          break
        case 404:
          message.error('资源不存在')
          break
        case 500:
          message.error(data.message || '服务器内部错误')
          break
        default:
          message.error('网络错误')
      }
    } else if (error.request) {
      message.error('网络连接失败')
    } else {
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 会员相关API
export const memberApi = {
  /**
   * 上传表格图片进行OCR识别
   */
  uploadForm(file, operator = 'system') {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('operator', operator)
    
    return api.post('/member/upload-form', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000 // OCR识别可能需要更长时间
    })
  },

  /**
   * 确认并保存会员信息
   */
  confirmAndSave(fields, rawOcrDataId, operator = 'system') {
    return api.post('/member/confirm-save', {
      fields,
      rawOcrDataId,
      operator
    })
  },

  /**
   * 根据ID查询会员信息
   */
  getMemberById(id) {
    return api.get(`/member/${id}`)
  },

  /**
   * 更新会员信息
   */
  updateMember(id, member, operator = 'system') {
    return api.put(`/member/${id}?operator=${operator}`, member)
  },

  /**
   * 删除会员信息
   */
  deleteMember(id, operator = 'system') {
    return api.delete(`/member/${id}?operator=${operator}`)
  },

  /**
   * 健康检查
   */
  health() {
    return api.get('/member/health')
  }
}

export default api
