import request from '@/utils/request'

/**
 * 智能匹配相关API
 */
const matchingApi = {
  /**
   * 获取匹配推荐
   * @param {Object} params 匹配参数
   * @param {number} params.memberId 会员ID
   * @param {number} params.topK 返回数量
   * @param {number} params.matchmakerId 红娘ID
   * @param {boolean} params.includeAiContent 是否包含AI内容
   * @param {Object} params.filter 过滤条件
   */
  getMatches(params) {
    return request({
      url: '/api/matching/matches',
      method: 'post',
      data: params
    })
  },

  /**
   * 提交匹配反馈
   * @param {Object} params 反馈参数
   * @param {number} params.matchId 匹配记录ID
   * @param {string} params.action 操作类型：view, reply, meet, success, reject
   * @param {string} params.feedback 反馈信息
   * @param {number} params.operatorId 操作人ID
   */
  submitFeedback(params) {
    return request({
      url: '/api/matching/feedback',
      method: 'post',
      params: params
    })
  },

  /**
   * 分页查询匹配记录
   * @param {Object} params 查询参数
   * @param {number} params.current 页码
   * @param {number} params.size 页大小
   * @param {number} params.memberId 会员ID
   * @param {number} params.status 状态
   * @param {number} params.matchmakerId 红娘ID
   */
  getMatchRecords(params) {
    return request({
      url: '/api/matching/records',
      method: 'get',
      params: params
    })
  },

  /**
   * 获取匹配统计数据
   * @param {Object} params 统计参数
   * @param {string} params.startTime 开始时间
   * @param {string} params.endTime 结束时间
   * @param {number} params.matchmakerId 红娘ID
   */
  getStatistics(params) {
    return request({
      url: '/api/matching/statistics',
      method: 'get',
      params: params
    })
  },

  /**
   * 获取今日统计数据
   * @param {number} matchmakerId 红娘ID
   */
  getTodayStatistics(matchmakerId) {
    return request({
      url: '/api/matching/statistics/today',
      method: 'get',
      params: { matchmakerId }
    })
  },

  /**
   * 获取会员匹配历史
   * @param {number} memberId 会员ID
   * @param {number} limit 限制数量
   */
  getMemberMatchHistory(memberId, limit = 20) {
    return request({
      url: `/api/matching/member/${memberId}/history`,
      method: 'get',
      params: { limit }
    })
  },

  /**
   * 更新会员画像向量
   * @param {number} memberId 会员ID
   */
  updateMemberEmbedding(memberId) {
    return request({
      url: `/api/matching/member/${memberId}/embedding/update`,
      method: 'post'
    })
  },

  /**
   * 批量更新会员画像向量
   * @param {Array} memberIds 会员ID列表
   */
  batchUpdateEmbeddings(memberIds) {
    return request({
      url: '/api/matching/embeddings/batch-update',
      method: 'post',
      data: memberIds
    })
  }
}

export default matchingApi
