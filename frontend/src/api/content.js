import request from '@/utils/request'

/**
 * AI内容生成相关API
 */
export const contentApi = {
  /**
   * 生成匹配推荐理由
   * @param {Object} data - 请求参数
   * @param {number} data.memberAId - 会员A的ID
   * @param {number} data.memberBId - 会员B的ID
   * @returns {Promise} API响应
   */
  generateMatchReason(data) {
    return request({
      url: '/api/content/match-reason',
      method: 'post',
      data
    })
  },

  /**
   * 生成破冰开场白
   * @param {Object} data - 请求参数
   * @param {number} data.memberAId - 会员A的ID
   * @param {number} data.memberBId - 会员B的ID
   * @returns {Promise} API响应
   */
  generateIceBreakers(data) {
    return request({
      url: '/api/content/ice-breakers',
      method: 'post',
      data
    })
  },

  /**
   * 生成约会建议
   * @param {Object} data - 请求参数
   * @param {number} data.memberAId - 会员A的ID
   * @param {number} data.memberBId - 会员B的ID
   * @param {string} data.city - 约会城市（可选）
   * @returns {Promise} API响应
   */
  generateDateSuggestions(data) {
    return request({
      url: '/api/content/date-suggestions',
      method: 'post',
      data
    })
  },

  /**
   * 生成个人介绍优化建议
   * @param {Object} data - 请求参数
   * @param {number} data.memberId - 会员ID
   * @returns {Promise} API响应
   */
  generateProfileOptimization(data) {
    return request({
      url: '/api/content/profile-optimization',
      method: 'post',
      data
    })
  },

  /**
   * 批量生成匹配内容
   * @param {Object} data - 请求参数
   * @param {number} data.memberAId - 会员A的ID
   * @param {number} data.memberBId - 会员B的ID
   * @param {string} data.city - 约会城市（可选）
   * @param {boolean} data.includeReason - 是否包含推荐理由
   * @param {boolean} data.includeIceBreakers - 是否包含开场白
   * @param {boolean} data.includeDateSuggestions - 是否包含约会建议
   * @returns {Promise} API响应
   */
  generateMatchContentBatch(data) {
    return request({
      url: '/api/content/match-content-batch',
      method: 'post',
      data
    })
  },

  /**
   * 检查AI内容生成服务健康状态
   * @returns {Promise} API响应
   */
  checkHealth() {
    return request({
      url: '/api/content/health',
      method: 'get'
    })
  }
}

export default contentApi
