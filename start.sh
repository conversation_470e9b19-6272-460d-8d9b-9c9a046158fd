#!/bin/bash

# 婚介所后台管理系统启动脚本

echo "=== 婚介所后台管理系统启动脚本 ==="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量
if [ -z "$QWEN_API_KEY" ]; then
    echo "警告: QWEN_API_KEY环境变量未设置，OCR功能将无法使用"
    echo "请设置: export QWEN_API_KEY=your-qwen-api-key"
fi

if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "警告: DEEPSEEK_API_KEY环境变量未设置，AI纠错功能将无法使用"
    echo "请设置: export DEEPSEEK_API_KEY=your-deepseek-api-key"
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p logs
mkdir -p data/uploads

# 构建后端应用
echo "构建后端应用..."
cd backend
if [ ! -f "target/matchmaking-system-1.0.0.jar" ]; then
    echo "构建Spring Boot应用..."
    mvn clean package -DskipTests
    if [ $? -ne 0 ]; then
        echo "错误: 后端构建失败"
        exit 1
    fi
fi
cd ..

# 启动服务
echo "启动Docker Compose服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 健康检查
echo "执行健康检查..."
sleep 10

# 检查后端服务
echo "检查后端服务..."
if curl -f http://localhost:8080/api/member/health > /dev/null 2>&1; then
    echo "✓ 后端服务启动成功"
else
    echo "✗ 后端服务启动失败"
fi

# 检查前端服务
echo "检查前端服务..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✓ 前端服务启动成功"
else
    echo "✗ 前端服务启动失败"
fi

echo ""
echo "=== 启动完成 ==="
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8080"
echo "数据库地址: localhost:3306"
echo ""
echo "查看日志: docker-compose logs -f"
echo "停止服务: docker-compose down"
echo ""
