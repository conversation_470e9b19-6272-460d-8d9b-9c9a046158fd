<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.matchmaking.mapper.MemberEmbeddingMapper">

    <!-- 批量插入或更新向量数据 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO member_embeddings (member_id, profile_vector, vector_version, create_time, update_time)
        VALUES
        <foreach collection="embeddings" item="item" separator=",">
            (#{item.memberId}, #{item.profileVector,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}, 
             #{item.vectorVersion}, NOW(), NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        profile_vector = VALUES(profile_vector),
        vector_version = VALUES(vector_version),
        update_time = NOW()
    </insert>

</mapper>
