/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/VectorEmbeddingServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/config/WebConfig.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/MatchRecordServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/controller/ContentController.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/dto/MatchResult.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/MatchRecord.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/dto/OcrResultDTO.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/util/TestDataGenerator.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/MemberEmbeddingMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/ValidationServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/MatchmakingSystemApplication.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/MemberPreferenceMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/SystemConfig.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/Member.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/VectorEmbeddingService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/controller/MemberController.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/MatchingEngineService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/controller/MatchingController.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/AuditLog.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/MatchingEngineServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/OcrService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/dto/MatchRequest.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/RawOcrDataMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/MemberServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/AiCorrectionServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/MemberMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/config/MybatisPlusConfig.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/MatchRecordMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/MemberPreference.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/RawOcrData.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/MatchRecordService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/ContentGeneratorService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/SystemConfigMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/MemberService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/controller/VectorController.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/common/Result.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/ValidationService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/AiCorrectionService.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/ContentGeneratorServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/mapper/AuditLogMapper.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/service/impl/OcrServiceImpl.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/controller/TestDataController.java
/Users/<USER>/Desktop/qoder_workspace/demo1/backend/src/main/java/com/matchmaking/entity/MemberEmbedding.java
