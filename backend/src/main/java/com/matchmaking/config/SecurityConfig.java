package com.matchmaking.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security配置
 * 临时禁用认证以便测试向量化系统
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护
            .csrf().disable()
            
            // 配置CORS
            .cors().configurationSource(corsConfigurationSource())
            
            .and()
            
            // 配置会话管理
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            
            .and()
            
            // 配置授权规则 - 临时允许所有请求
            .authorizeRequests()
            .antMatchers("/vector/**").permitAll()  // 向量化API
            .antMatchers("/test-data/**").permitAll()  // 测试数据API
            .antMatchers("/members/**").permitAll()  // 会员管理API
            .antMatchers("/ocr/**").permitAll()  // OCR API
            .antMatchers("/matching/**").permitAll()  // 匹配API
            .antMatchers("/swagger-ui/**").permitAll()  // Swagger UI
            .antMatchers("/swagger-resources/**").permitAll()  // Swagger资源
            .antMatchers("/v2/api-docs").permitAll()  // API文档
            .antMatchers("/webjars/**").permitAll()  // Web资源
            .antMatchers("/actuator/**").permitAll()  // 监控端点
            .anyRequest().permitAll()  // 临时允许所有其他请求
            
            .and()
            
            // 禁用HTTP Basic认证
            .httpBasic().disable()
            
            // 禁用表单登录
            .formLogin().disable()
            
            // 禁用登出
            .logout().disable();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的源
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        
        // 允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        
        // 允许的请求头
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // 允许发送Cookie
        configuration.setAllowCredentials(true);
        
        // 预检请求的缓存时间
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}
