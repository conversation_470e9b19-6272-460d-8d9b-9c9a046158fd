package com.matchmaking.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审计日志实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("audit_log")
public class AuditLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 字段名
     */
    @TableField("field_name")
    private String fieldName;

    /**
     * 原值
     */
    @TableField("old_value")
    private String oldValue;

    /**
     * 新值
     */
    @TableField("new_value")
    private String newValue;

    /**
     * 操作类型：1-OCR识别，2-规则校验，3-AI纠错，4-人工修改
     */
    @TableField("operation_type")
    private Integer operationType;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作人类型：1-系统，2-红娘，3-管理员
     */
    @TableField("operator_type")
    private Integer operatorType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 操作时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
