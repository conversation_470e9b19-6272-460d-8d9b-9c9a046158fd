package com.matchmaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 匹配记录实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName(value = "match_records", autoResultMap = true)
public class MatchRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会员A ID
     */
    private Long memberAId;

    /**
     * 会员B ID
     */
    private Long memberBId;

    /**
     * 红娘ID
     */
    private Long matchmakerId;

    /**
     * 匹配分数(0-1)
     */
    private BigDecimal matchScore;

    /**
     * 语义相似度
     */
    private BigDecimal semanticSimilarity;

    /**
     * 硬性条件分数
     */
    private BigDecimal hardConditionsScore;

    /**
     * 活跃度分数
     */
    private BigDecimal activityScore;

    /**
     * AI生成的推荐理由
     */
    private String reason;

    /**
     * 破冰话术(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> iceBreakers;

    /**
     * 约会建议(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> dateSuggestions;

    /**
     * 状态：0-推荐，1-查看，2-回复，3-约见，4-成功，5-失败
     */
    private Integer status;

    /**
     * 反馈信息
     */
    private String feedback;

    /**
     * 查看时间
     */
    private LocalDateTime viewedTime;

    /**
     * 回复时间
     */
    private LocalDateTime repliedTime;

    /**
     * 约见时间
     */
    private LocalDateTime metTime;

    /**
     * 结果确认时间
     */
    private LocalDateTime resultTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 匹配状态枚举
    public enum Status {
        RECOMMENDED(0, "推荐"),
        VIEWED(1, "查看"),
        REPLIED(2, "回复"),
        MET(3, "约见"),
        SUCCESS(4, "成功"),
        FAILED(5, "失败");

        private final int code;
        private final String description;

        Status(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(int code) {
            for (Status status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status code: " + code);
        }
    }
}
