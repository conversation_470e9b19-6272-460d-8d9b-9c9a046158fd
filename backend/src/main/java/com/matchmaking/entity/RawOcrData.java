package com.matchmaking.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 原始OCR数据实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("raw_ocr_data")
public class RawOcrData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联会员ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 原始图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * OCR识别原始JSON结果
     */
    @TableField("raw_json")
    private String rawJson;

    /**
     * OCR识别置信度
     */
    @TableField("ocr_confidence")
    private BigDecimal ocrConfidence;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
