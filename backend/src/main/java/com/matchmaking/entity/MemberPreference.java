package com.matchmaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员偏好配置实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName(value = "member_preferences", autoResultMap = true)
public class MemberPreference {

    /**
     * 会员ID
     */
    @TableId(type = IdType.INPUT)
    private Long memberId;

    /**
     * 最小年龄偏好
     */
    private Integer ageMin;

    /**
     * 最大年龄偏好
     */
    private Integer ageMax;

    /**
     * 最小身高偏好(cm)
     */
    private Integer heightMin;

    /**
     * 最大身高偏好(cm)
     */
    private Integer heightMax;

    /**
     * 最低学历要求
     */
    private Integer educationMin;

    /**
     * 最低收入要求
     */
    private Integer incomeMin;

    /**
     * 偏好城市(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> preferredCities;

    /**
     * 不接受的条件(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> dealBreakers;

    /**
     * 是否启用偏好过滤：1-启用，0-禁用
     */
    private Integer isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
