package com.matchmaking.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 会员信息实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("member")
public class Member implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 性别：1-男，2-女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 出生年月
     */
    @TableField("birth_date")
    private LocalDate birthDate;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 民族
     */
    @TableField("ethnicity")
    private String ethnicity;

    /**
     * 身高(cm)
     */
    @TableField("height")
    private Integer height;

    /**
     * 体重(kg)
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 学历：1-高中，2-专科，3-本科，4-硕士，5-博士，6-其他
     */
    @TableField("education_level")
    private Integer educationLevel;

    /**
     * 专业
     */
    @TableField("major")
    private String major;

    /**
     * 职业
     */
    @TableField("occupation")
    private String occupation;

    /**
     * 单位性质：1-国企，2-外企，3-私企，4-事业单位，5-公务员，6-自由职业
     */
    @TableField("company_type")
    private Integer companyType;

    /**
     * 月收入：1-2000以下，2-2000-4000，3-4000-8000，4-8000-20000，5-20000以上
     */
    @TableField("monthly_income")
    private Integer monthlyIncome;



    /**
     * 房产情况：1-有房，2-无房，3-按揭
     */
    @TableField("house_status")
    private Integer houseStatus;

    /**
     * 车产情况：1-有车，2-无车
     */
    @TableField("car_status")
    private Integer carStatus;

    /**
     * 父母职业
     */
    @TableField("parents_occupation")
    private String parentsOccupation;

    /**
     * 兄弟姐妹情况
     */
    @TableField("siblings")
    private String siblings;

    /**
     * 期望年龄最小值
     */
    @TableField("preferred_age_min")
    private Integer preferredAgeMin;

    /**
     * 期望年龄最大值
     */
    @TableField("preferred_age_max")
    private Integer preferredAgeMax;

    /**
     * 期望学历
     */
    @TableField("preferred_education")
    private Integer preferredEducation;

    /**
     * 期望身高最小值
     */
    @TableField("preferred_height_min")
    private Integer preferredHeightMin;

    /**
     * 期望婚姻状况
     */
    @TableField("preferred_marriage_status")
    private Integer preferredMarriageStatus;

    /**
     * 兴趣爱好
     */
    @TableField("hobbies")
    private String hobbies;

    /**
     * 价值观/备注
     */
    @TableField("values_notes")
    private String valuesNotes;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 微信号
     */
    @TableField("wechat")
    private String wechat;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 婚姻状况：1-未婚，2-离异，3-丧偶
     */
    @TableField("marital_status")
    private Integer maritalStatus;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 微信ID
     */
    @TableField("wechat_id")
    private String wechatId;

    /**
     * 个人介绍
     */
    @TableField("personal_introduction")
    private String personalIntroduction;

    /**
     * 自我描述
     */
    @TableField("self_description")
    private String selfDescription;

    /**
     * 是否活跃：1-活跃，0-不活跃
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 最后活跃时间
     */
    @TableField("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 状态：1-正常，2-暂停，3-删除
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人（红娘账号）
     */
    @TableField("creator")
    private String creator;
}
