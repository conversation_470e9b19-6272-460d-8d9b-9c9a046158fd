package com.matchmaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员画像向量实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName(value = "member_embeddings", autoResultMap = true)
public class MemberEmbedding {

    /**
     * 会员ID
     */
    @TableId(type = IdType.INPUT)
    private Long memberId;

    /**
     * 用户画像向量(384维)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Double> profileVector;

    /**
     * 向量版本
     */
    private String vectorVersion;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
