package com.matchmaking.service.impl;

import com.matchmaking.dto.OcrResultDTO;
import com.matchmaking.service.ValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 数据校验服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ValidationServiceImpl implements ValidationService {

    @Value("${matchmaking.validation.age-range:18-80}")
    private String ageRange;

    @Value("${matchmaking.validation.height-range:140-220}")
    private String heightRange;

    @Value("${matchmaking.validation.weight-range:35-200}")
    private String weightRange;

    // 手机号正则
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 学历枚举
    private static final Set<Integer> EDUCATION_LEVELS = new HashSet<>(Arrays.asList(1, 2, 3, 4, 5, 6));

    // 性别枚举
    private static final Set<Integer> GENDERS = new HashSet<>(Arrays.asList(1, 2));

    // 婚姻状况枚举
    private static final Set<Integer> MARRIAGE_STATUS = new HashSet<>(Arrays.asList(1, 2, 3));

    // 单位性质枚举
    private static final Set<Integer> COMPANY_TYPES = new HashSet<>(Arrays.asList(1, 2, 3, 4, 5, 6));

    // 月收入枚举
    private static final Set<Integer> INCOME_LEVELS = new HashSet<>(Arrays.asList(1, 2, 3, 4, 5));

    // 房产情况枚举
    private static final Set<Integer> HOUSE_STATUS = new HashSet<>(Arrays.asList(1, 2, 3));

    // 车产情况枚举
    private static final Set<Integer> CAR_STATUS = new HashSet<>(Arrays.asList(1, 2));

    @Override
    public List<OcrResultDTO.ValidationError> validateOcrResult(Map<String, Object> fields) {
        List<OcrResultDTO.ValidationError> errors = new ArrayList<>();

        // 校验必填字段
        validateRequiredFields(fields, errors);

        // 校验各个字段
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            OcrResultDTO.ValidationError error = validateField(fieldName, value, fields);
            if (error != null) {
                errors.add(error);
            }
        }

        // 关联校验
        validateRelatedFields(fields, errors);

        return errors;
    }

    @Override
    public OcrResultDTO.ValidationError validateField(String fieldName, Object value, Map<String, Object> allFields) {
        if (value == null) {
            return null; // null值由必填校验处理
        }

        try {
            switch (fieldName) {
                case "name":
                    return validateName(value);
                case "gender":
                    return validateGender(value);
                case "age":
                    return validateAge(value);
                case "birthDate":
                    return validateBirthDate(value);
                case "height":
                    return validateHeight(value);
                case "weight":
                    return validateWeight(value);
                case "educationLevel":
                    return validateEducationLevel(value);
                case "monthlyIncome":
                    return validateMonthlyIncome(value);
                case "marriageStatus":
                    return validateMarriageStatus(value);
                case "companyType":
                    return validateCompanyType(value);
                case "houseStatus":
                    return validateHouseStatus(value);
                case "carStatus":
                    return validateCarStatus(value);
                case "phone":
                    return validatePhone(value);
                case "preferredAgeMin":
                case "preferredAgeMax":
                    return validatePreferredAge(value);
                case "preferredHeightMin":
                    return validatePreferredHeight(value);
                case "preferredEducation":
                    return validateEducationLevel(value);
                case "preferredMarriageStatus":
                    return validateMarriageStatus(value);
                default:
                    return null; // 其他字段不校验
            }
        } catch (Exception e) {
            log.warn("校验字段{}时发生异常: {}", fieldName, e.getMessage());
            return createValidationError(fieldName, "VALIDATION_ERROR", "字段校验异常", value, null);
        }
    }

    @Override
    public String getFieldValidRange(String fieldName) {
        switch (fieldName) {
            case "age":
            case "preferredAgeMin":
            case "preferredAgeMax":
                return ageRange;
            case "height":
            case "preferredHeightMin":
                return heightRange;
            case "weight":
                return weightRange;
            case "gender":
                return "1-男，2-女";
            case "educationLevel":
            case "preferredEducation":
                return "1-高中，2-专科，3-本科，4-硕士，5-博士，6-其他";
            case "marriageStatus":
            case "preferredMarriageStatus":
                return "1-未婚，2-离异，3-丧偶";
            case "monthlyIncome":
                return "1-2000以下，2-2000-4000，3-4000-8000，4-8000-20000，5-20000以上";
            case "companyType":
                return "1-国企，2-外企，3-私企，4-事业单位，5-公务员，6-自由职业";
            case "houseStatus":
                return "1-有房，2-无房，3-按揭";
            case "carStatus":
                return "1-有车，2-无车";
            case "phone":
                return "11位手机号码";
            default:
                return "无特殊限制";
        }
    }

    /**
     * 校验必填字段
     */
    private void validateRequiredFields(Map<String, Object> fields, List<OcrResultDTO.ValidationError> errors) {
        String[] requiredFields = {"name", "gender", "age", "marriageStatus"};

        for (String fieldName : requiredFields) {
            Object value = fields.get(fieldName);
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                errors.add(createValidationError(fieldName, "REQUIRED", "必填字段不能为空", value, null));
            }
        }
    }

    /**
     * 关联字段校验
     */
    private void validateRelatedFields(Map<String, Object> fields, List<OcrResultDTO.ValidationError> errors) {
        // 年龄与出生日期一致性校验
        validateAgeAndBirthDate(fields, errors);

        // 择偶年龄范围校验
        validatePreferredAgeRange(fields, errors);
    }

    /**
     * 校验年龄与出生日期一致性
     */
    private void validateAgeAndBirthDate(Map<String, Object> fields, List<OcrResultDTO.ValidationError> errors) {
        Object ageObj = fields.get("age");
        Object birthDateObj = fields.get("birthDate");

        if (ageObj != null && birthDateObj != null) {
            try {
                int age = Integer.parseInt(ageObj.toString());
                String birthDateStr = birthDateObj.toString();

                // 解析出生日期
                LocalDate birthDate;
                if (birthDateStr.length() == 7) { // YYYY-MM格式
                    birthDate = LocalDate.parse(birthDateStr + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                } else {
                    birthDate = LocalDate.parse(birthDateStr);
                }

                // 计算年龄
                int calculatedAge = LocalDate.now().getYear() - birthDate.getYear();

                // 允许1岁误差
                if (Math.abs(age - calculatedAge) > 1) {
                    errors.add(createValidationError("age", "INCONSISTENT", 
                        String.format("年龄(%d)与出生日期(%s)不一致，计算年龄为%d", age, birthDateStr, calculatedAge), 
                        age, calculatedAge));
                }
            } catch (Exception e) {
                log.warn("年龄与出生日期一致性校验失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 校验择偶年龄范围
     */
    private void validatePreferredAgeRange(Map<String, Object> fields, List<OcrResultDTO.ValidationError> errors) {
        Object minAgeObj = fields.get("preferredAgeMin");
        Object maxAgeObj = fields.get("preferredAgeMax");

        if (minAgeObj != null && maxAgeObj != null) {
            try {
                int minAge = Integer.parseInt(minAgeObj.toString());
                int maxAge = Integer.parseInt(maxAgeObj.toString());

                if (minAge > maxAge) {
                    errors.add(createValidationError("preferredAgeMin", "RANGE_ERROR", 
                        "期望年龄最小值不能大于最大值", minAge, null));
                }
            } catch (Exception e) {
                log.warn("择偶年龄范围校验失败: {}", e.getMessage());
            }
        }
    }

    // 各字段具体校验方法
    private OcrResultDTO.ValidationError validateName(Object value) {
        String name = value.toString().trim();
        if (name.length() < 2 || name.length() > 10) {
            return createValidationError("name", "LENGTH_ERROR", "姓名长度应在2-10个字符之间", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateGender(Object value) {
        try {
            int gender = Integer.parseInt(value.toString());
            if (!GENDERS.contains(gender)) {
                return createValidationError("gender", "ENUM_ERROR", "性别值无效，应为1(男)或2(女)", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("gender", "TYPE_ERROR", "性别应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateAge(Object value) {
        try {
            int age = Integer.parseInt(value.toString());
            String[] range = ageRange.split("-");
            int minAge = Integer.parseInt(range[0]);
            int maxAge = Integer.parseInt(range[1]);

            if (age < minAge || age > maxAge) {
                return createValidationError("age", "RANGE_ERROR", 
                    String.format("年龄应在%d-%d岁之间", minAge, maxAge), value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("age", "TYPE_ERROR", "年龄应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateBirthDate(Object value) {
        try {
            String birthDateStr = value.toString();
            // 支持YYYY-MM或YYYY-MM-DD格式
            if (birthDateStr.matches("\\d{4}-\\d{2}")) {
                LocalDate.parse(birthDateStr + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else if (birthDateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                LocalDate.parse(birthDateStr);
            } else {
                return createValidationError("birthDate", "FORMAT_ERROR", "出生日期格式错误，应为YYYY-MM或YYYY-MM-DD", value, null);
            }
        } catch (Exception e) {
            return createValidationError("birthDate", "FORMAT_ERROR", "出生日期格式错误", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateHeight(Object value) {
        try {
            int height = Integer.parseInt(value.toString());
            String[] range = heightRange.split("-");
            int minHeight = Integer.parseInt(range[0]);
            int maxHeight = Integer.parseInt(range[1]);

            if (height < minHeight || height > maxHeight) {
                return createValidationError("height", "RANGE_ERROR", 
                    String.format("身高应在%d-%dcm之间", minHeight, maxHeight), value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("height", "TYPE_ERROR", "身高应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateWeight(Object value) {
        try {
            int weight = Integer.parseInt(value.toString());
            String[] range = weightRange.split("-");
            int minWeight = Integer.parseInt(range[0]);
            int maxWeight = Integer.parseInt(range[1]);

            if (weight < minWeight || weight > maxWeight) {
                return createValidationError("weight", "RANGE_ERROR", 
                    String.format("体重应在%d-%dkg之间", minWeight, maxWeight), value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("weight", "TYPE_ERROR", "体重应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateEducationLevel(Object value) {
        try {
            int education = Integer.parseInt(value.toString());
            if (!EDUCATION_LEVELS.contains(education)) {
                return createValidationError("educationLevel", "ENUM_ERROR", 
                    "学历值无效，应为1-6之间的数字", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("educationLevel", "TYPE_ERROR", "学历应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateMonthlyIncome(Object value) {
        try {
            int income = Integer.parseInt(value.toString());
            if (!INCOME_LEVELS.contains(income)) {
                return createValidationError("monthlyIncome", "ENUM_ERROR", 
                    "收入等级无效，应为1-5之间的数字", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("monthlyIncome", "TYPE_ERROR", "收入等级应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateMarriageStatus(Object value) {
        try {
            int status = Integer.parseInt(value.toString());
            if (!MARRIAGE_STATUS.contains(status)) {
                return createValidationError("marriageStatus", "ENUM_ERROR", 
                    "婚姻状况无效，应为1(未婚)、2(离异)或3(丧偶)", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("marriageStatus", "TYPE_ERROR", "婚姻状况应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateCompanyType(Object value) {
        try {
            int type = Integer.parseInt(value.toString());
            if (!COMPANY_TYPES.contains(type)) {
                return createValidationError("companyType", "ENUM_ERROR", 
                    "单位性质无效，应为1-6之间的数字", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("companyType", "TYPE_ERROR", "单位性质应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateHouseStatus(Object value) {
        try {
            int status = Integer.parseInt(value.toString());
            if (!HOUSE_STATUS.contains(status)) {
                return createValidationError("houseStatus", "ENUM_ERROR", 
                    "房产情况无效，应为1(有房)、2(无房)或3(按揭)", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("houseStatus", "TYPE_ERROR", "房产情况应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validateCarStatus(Object value) {
        try {
            int status = Integer.parseInt(value.toString());
            if (!CAR_STATUS.contains(status)) {
                return createValidationError("carStatus", "ENUM_ERROR", 
                    "车产情况无效，应为1(有车)或2(无车)", value, null);
            }
        } catch (NumberFormatException e) {
            return createValidationError("carStatus", "TYPE_ERROR", "车产情况应为数字", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validatePhone(Object value) {
        String phone = value.toString().trim();
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return createValidationError("phone", "FORMAT_ERROR", "手机号格式错误", value, null);
        }
        return null;
    }

    private OcrResultDTO.ValidationError validatePreferredAge(Object value) {
        return validateAge(value); // 复用年龄校验逻辑
    }

    private OcrResultDTO.ValidationError validatePreferredHeight(Object value) {
        return validateHeight(value); // 复用身高校验逻辑
    }

    /**
     * 创建校验错误对象
     */
    private OcrResultDTO.ValidationError createValidationError(String fieldName, String errorType, 
                                                               String errorMessage, Object originalValue, Object suggestedValue) {
        OcrResultDTO.ValidationError error = new OcrResultDTO.ValidationError();
        error.setFieldName(fieldName);
        error.setErrorType(errorType);
        error.setErrorMessage(errorMessage);
        error.setOriginalValue(originalValue);
        error.setSuggestedValue(suggestedValue);
        return error;
    }
}
