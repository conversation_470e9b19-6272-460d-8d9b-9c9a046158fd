package com.matchmaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.matchmaking.dto.OcrResultDTO;
import com.matchmaking.entity.AuditLog;
import com.matchmaking.entity.Member;
import com.matchmaking.entity.RawOcrData;
import com.matchmaking.mapper.AuditLogMapper;
import com.matchmaking.mapper.MemberMapper;
import com.matchmaking.mapper.RawOcrDataMapper;
import com.matchmaking.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 会员信息服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class MemberServiceImpl extends ServiceImpl<MemberMapper, Member> implements MemberService {

    @Resource
    private OcrService ocrService;

    @Resource
    private ValidationService validationService;

    @Resource
    private AiCorrectionService aiCorrectionService;

    @Resource
    private RawOcrDataMapper rawOcrDataMapper;

    @Resource
    private AuditLogMapper auditLogMapper;

    @Value("${matchmaking.file.upload-path}")
    private String uploadPath;

    @Value("${matchmaking.file.base-url}")
    private String baseUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OcrResultDTO processFormImage(MultipartFile imageFile, String operator) {
        try {
            // 1. 保存上传的图片文件
            String imageUrl = saveUploadedFile(imageFile);
            
            // 2. 调用OCR识别
            log.info("开始OCR识别，图片URL: {}", imageUrl);
            OcrResultDTO ocrResult = ocrService.recognizeForm(imageFile);
            
            // 3. 规则引擎校验
            log.info("开始规则校验");
            List<OcrResultDTO.ValidationError> validationErrors = 
                validationService.validateOcrResult(ocrResult.getFields());
            ocrResult.setValidationErrors(validationErrors);
            
            // 4. AI纠错（如果有校验错误且AI服务可用）
            if (!validationErrors.isEmpty() && aiCorrectionService.isAvailable()) {
                log.info("开始AI纠错，错误数量: {}", validationErrors.size());
                Map<String, Object> aiCorrections = aiCorrectionService.correctErrors(
                    ocrResult.getFields(), validationErrors, ocrResult.getRawResponse());
                ocrResult.setAiCorrections(aiCorrections);
            }
            
            // 5. 保存原始OCR数据
            RawOcrData rawOcrData = new RawOcrData();
            rawOcrData.setImageUrl(imageUrl);
            rawOcrData.setRawJson(JSON.toJSONString(ocrResult));
            rawOcrData.setOcrConfidence(ocrResult.getConfidence());
            rawOcrDataMapper.insert(rawOcrData);
            
            // 6. 记录审计日志
            recordAuditLog(null, "OCR_PROCESS", null, "OCR识别完成", 
                1, operator, 1, "图片: " + imageUrl);
            
            log.info("OCR处理完成，原始数据ID: {}", rawOcrData.getId());
            return ocrResult;
            
        } catch (Exception e) {
            log.error("处理表格图片失败", e);
            throw new RuntimeException("处理表格图片失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Member confirmAndSaveMember(Map<String, Object> fields, Long rawOcrDataId, String operator) {
        try {
            // 1. 创建会员对象
            Member member = new Member();
            
            // 2. 填充字段数据
            populateMemberFromFields(member, fields);
            member.setCreator(operator);
            member.setStatus(1); // 正常状态
            
            // 3. 保存会员信息
            this.save(member);
            
            // 4. 更新原始OCR数据的关联会员ID
            if (rawOcrDataId != null) {
                RawOcrData rawOcrData = rawOcrDataMapper.selectById(rawOcrDataId);
                if (rawOcrData != null) {
                    rawOcrData.setMemberId(member.getId());
                    rawOcrDataMapper.updateById(rawOcrData);
                }
            }
            
            // 5. 记录审计日志
            recordAuditLog(member.getId(), "MEMBER_CREATE", null, "会员创建", 
                4, operator, 2, "人工确认并保存");
            
            log.info("会员信息保存成功，ID: {}, 姓名: {}", member.getId(), member.getName());
            return member;
            
        } catch (Exception e) {
            log.error("保存会员信息失败", e);
            throw new RuntimeException("保存会员信息失败: " + e.getMessage());
        }
    }

    @Override
    public void recordAuditLog(Long memberId, String fieldName, String oldValue, String newValue,
                              Integer operationType, String operator, Integer operatorType, String remark) {
        try {
            AuditLog auditLog = new AuditLog();
            auditLog.setMemberId(memberId);
            auditLog.setFieldName(fieldName);
            auditLog.setOldValue(oldValue);
            auditLog.setNewValue(newValue);
            auditLog.setOperationType(operationType);
            auditLog.setOperator(operator);
            auditLog.setOperatorType(operatorType);
            auditLog.setRemark(remark);
            
            auditLogMapper.insert(auditLog);
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
            // 审计日志失败不影响主流程
        }
    }

    /**
     * 保存上传的文件
     */
    private String saveUploadedFile(MultipartFile file) throws IOException {
        // 创建上传目录
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String filename = UUID.randomUUID().toString() + extension;
        
        // 保存文件
        File targetFile = new File(uploadDir, filename);
        file.transferTo(targetFile);
        
        // 返回访问URL
        return baseUrl + filename;
    }

    /**
     * 从字段数据填充会员对象
     */
    private void populateMemberFromFields(Member member, Map<String, Object> fields) {
        // 基础信息
        if (fields.get("name") != null) {
            member.setName(fields.get("name").toString());
        }
        if (fields.get("gender") != null) {
            member.setGender(Integer.valueOf(fields.get("gender").toString()));
        }
        if (fields.get("age") != null) {
            member.setAge(Integer.valueOf(fields.get("age").toString()));
        }
        if (fields.get("birthDate") != null) {
            String birthDateStr = fields.get("birthDate").toString();
            try {
                if (birthDateStr.length() == 7) { // YYYY-MM格式
                    member.setBirthDate(LocalDate.parse(birthDateStr + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                } else {
                    member.setBirthDate(LocalDate.parse(birthDateStr));
                }
            } catch (Exception e) {
                log.warn("解析出生日期失败: {}", birthDateStr);
            }
        }
        if (fields.get("ethnicity") != null) {
            member.setEthnicity(fields.get("ethnicity").toString());
        }
        
        // 外貌信息
        if (fields.get("height") != null) {
            member.setHeight(Integer.valueOf(fields.get("height").toString()));
        }
        if (fields.get("weight") != null) {
            member.setWeight(Integer.valueOf(fields.get("weight").toString()));
        }
        
        // 学历职业
        if (fields.get("educationLevel") != null) {
            member.setEducationLevel(Integer.valueOf(fields.get("educationLevel").toString()));
        }
        if (fields.get("major") != null) {
            member.setMajor(fields.get("major").toString());
        }
        if (fields.get("occupation") != null) {
            member.setOccupation(fields.get("occupation").toString());
        }
        if (fields.get("companyType") != null) {
            member.setCompanyType(Integer.valueOf(fields.get("companyType").toString()));
        }
        if (fields.get("monthlyIncome") != null) {
            member.setMonthlyIncome(Integer.valueOf(fields.get("monthlyIncome").toString()));
        }
        
        // 婚姻房产
        if (fields.get("marriageStatus") != null) {
            member.setMaritalStatus(Integer.valueOf(fields.get("marriageStatus").toString()));
        }
        if (fields.get("houseStatus") != null) {
            member.setHouseStatus(Integer.valueOf(fields.get("houseStatus").toString()));
        }
        if (fields.get("carStatus") != null) {
            member.setCarStatus(Integer.valueOf(fields.get("carStatus").toString()));
        }
        
        // 家庭情况
        if (fields.get("parentsOccupation") != null) {
            member.setParentsOccupation(fields.get("parentsOccupation").toString());
        }
        if (fields.get("siblings") != null) {
            member.setSiblings(fields.get("siblings").toString());
        }
        
        // 择偶条件
        if (fields.get("preferredAgeMin") != null) {
            member.setPreferredAgeMin(Integer.valueOf(fields.get("preferredAgeMin").toString()));
        }
        if (fields.get("preferredAgeMax") != null) {
            member.setPreferredAgeMax(Integer.valueOf(fields.get("preferredAgeMax").toString()));
        }
        if (fields.get("preferredEducation") != null) {
            member.setPreferredEducation(Integer.valueOf(fields.get("preferredEducation").toString()));
        }
        if (fields.get("preferredHeightMin") != null) {
            member.setPreferredHeightMin(Integer.valueOf(fields.get("preferredHeightMin").toString()));
        }
        if (fields.get("preferredMarriageStatus") != null) {
            member.setPreferredMarriageStatus(Integer.valueOf(fields.get("preferredMarriageStatus").toString()));
        }
        
        // 其他信息
        if (fields.get("hobbies") != null) {
            member.setHobbies(fields.get("hobbies").toString());
        }
        if (fields.get("valuesNotes") != null) {
            member.setValuesNotes(fields.get("valuesNotes").toString());
        }
        if (fields.get("phone") != null) {
            member.setPhone(fields.get("phone").toString());
        }
        if (fields.get("wechat") != null) {
            member.setWechat(fields.get("wechat").toString());
        }
    }
}
