package com.matchmaking.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.matchmaking.entity.MatchRecord;
import com.matchmaking.mapper.MatchRecordMapper;
import com.matchmaking.service.MatchRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 匹配记录服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatchRecordServiceImpl implements MatchRecordService {

    private final MatchRecordMapper matchRecordMapper;

    @Override
    public boolean submitFeedback(Long matchId, String action, String feedback, Long operatorId) {
        try {
            MatchRecord record = matchRecordMapper.selectById(matchId);
            if (record == null) {
                log.error("匹配记录不存在: {}", matchId);
                return false;
            }

            // 更新状态和时间
            LocalDateTime now = LocalDateTime.now();
            switch (action.toLowerCase()) {
                case "view":
                    record.setStatus(MatchRecord.Status.VIEWED.getCode());
                    record.setViewedTime(now);
                    break;
                case "reply":
                    record.setStatus(MatchRecord.Status.REPLIED.getCode());
                    record.setRepliedTime(now);
                    break;
                case "meet":
                    record.setStatus(MatchRecord.Status.MET.getCode());
                    record.setMetTime(now);
                    break;
                case "success":
                    record.setStatus(MatchRecord.Status.SUCCESS.getCode());
                    record.setResultTime(now);
                    break;
                case "reject":
                case "failed":
                    record.setStatus(MatchRecord.Status.FAILED.getCode());
                    record.setResultTime(now);
                    break;
                default:
                    log.error("未知的操作类型: {}", action);
                    return false;
            }

            record.setFeedback(feedback);
            record.setUpdateTime(now);

            int updated = matchRecordMapper.updateById(record);
            log.info("更新匹配记录 {} 状态为 {}", matchId, action);
            return updated > 0;

        } catch (Exception e) {
            log.error("提交匹配反馈失败", e);
            return false;
        }
    }

    @Override
    public IPage<Map<String, Object>> getMatchRecords(Page<Map<String, Object>> page, 
                                                      Long memberId, Integer status, Long matchmakerId) {
        return matchRecordMapper.selectMatchRecordsWithMemberInfo(page, memberId, status, matchmakerId);
    }

    @Override
    public Map<String, Object> getMatchStatistics(LocalDateTime startTime, LocalDateTime endTime, Long matchmakerId) {
        Map<String, Object> rawStats = matchRecordMapper.getMatchStatistics(startTime, endTime, matchmakerId);
        
        // 计算百分比
        Map<String, Object> stats = new HashMap<>();
        Long totalMatches = (Long) rawStats.get("total_matches");
        Long viewedCount = (Long) rawStats.get("viewed_count");
        Long repliedCount = (Long) rawStats.get("replied_count");
        Long metCount = (Long) rawStats.get("met_count");
        Long successCount = (Long) rawStats.get("success_count");
        BigDecimal avgScore = (BigDecimal) rawStats.get("avg_score");

        stats.put("totalMatches", totalMatches != null ? totalMatches : 0L);
        stats.put("viewedCount", viewedCount != null ? viewedCount : 0L);
        stats.put("repliedCount", repliedCount != null ? repliedCount : 0L);
        stats.put("metCount", metCount != null ? metCount : 0L);
        stats.put("successCount", successCount != null ? successCount : 0L);

        // 计算转化率
        if (totalMatches != null && totalMatches > 0) {
            stats.put("viewRate", calculatePercentage(viewedCount, totalMatches));
            stats.put("replyRate", calculatePercentage(repliedCount, totalMatches));
            stats.put("meetRate", calculatePercentage(metCount, totalMatches));
            stats.put("successRate", calculatePercentage(successCount, totalMatches));
        } else {
            stats.put("viewRate", 0.0);
            stats.put("replyRate", 0.0);
            stats.put("meetRate", 0.0);
            stats.put("successRate", 0.0);
        }

        stats.put("avgScore", avgScore != null ? avgScore.doubleValue() : 0.0);

        return stats;
    }

    @Override
    public Map<String, Object> getTodayStatistics(Long matchmakerId) {
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.now().with(LocalTime.MAX);
        return getMatchStatistics(startOfDay, endOfDay, matchmakerId);
    }

    @Override
    public List<MatchRecord> getMemberMatchHistory(Long memberId, Integer limit) {
        return matchRecordMapper.selectMemberMatchHistory(memberId, limit != null ? limit : 20);
    }

    /**
     * 计算百分比
     */
    private double calculatePercentage(Long numerator, Long denominator) {
        if (denominator == null || denominator == 0) {
            return 0.0;
        }
        if (numerator == null) {
            numerator = 0L;
        }
        return BigDecimal.valueOf(numerator)
                .divide(BigDecimal.valueOf(denominator), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
    }
}
