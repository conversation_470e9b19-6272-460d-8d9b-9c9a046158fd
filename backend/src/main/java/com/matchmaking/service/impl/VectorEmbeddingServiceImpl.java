package com.matchmaking.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.matchmaking.entity.Member;
import com.matchmaking.entity.MemberEmbedding;
import com.matchmaking.mapper.MemberEmbeddingMapper;
import com.matchmaking.service.VectorEmbeddingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.io.support.ClassicRequestBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 向量化服务实现
 * 基于m3e-small模型的文本向量化
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VectorEmbeddingServiceImpl implements VectorEmbeddingService {

    private final MemberEmbeddingMapper memberEmbeddingMapper;
    private final Gson gson = new Gson();

    @Value("${vector.model.api-url:http://localhost:8001/embeddings}")
    private String vectorApiUrl;

    @Value("${vector.model.name:m3e-small}")
    private String modelName;

    @Value("${vector.model.dimension:384}")
    private int vectorDimension;

    @Value("${vector.model.timeout:30000}")
    private int timeoutMs;

    @Override
    public List<Double> textToVector(String text) {
        if (text == null || text.trim().isEmpty()) {
            return generateZeroVector();
        }

        try {
            return callVectorAPI(Collections.singletonList(text)).get(0);
        } catch (Exception e) {
            log.error("文本向量化失败: {}", text, e);
            return generateZeroVector();
        }
    }

    @Override
    public List<List<Double>> batchTextToVector(List<String> texts) {
        if (texts == null || texts.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return callVectorAPI(texts);
        } catch (Exception e) {
            log.error("批量文本向量化失败", e);
            // 返回零向量列表
            return texts.stream()
                    .map(t -> generateZeroVector())
                    .collect(Collectors.toList());
        }
    }

    @Override
    public String generateMemberProfileText(Member member) {
        StringBuilder profile = new StringBuilder();

        // 基本信息
        if (member.getName() != null) {
            profile.append("姓名：").append(member.getName()).append(" ");
        }
        
        profile.append("性别：").append(member.getGender() == 1 ? "男" : "女").append(" ");
        
        if (member.getAge() != null) {
            profile.append("年龄：").append(member.getAge()).append("岁 ");
        }
        
        if (member.getCity() != null) {
            profile.append("城市：").append(member.getCity()).append(" ");
        }

        // 外貌信息
        if (member.getHeight() != null) {
            profile.append("身高：").append(member.getHeight()).append("cm ");
        }
        
        if (member.getWeight() != null) {
            profile.append("体重：").append(member.getWeight()).append("kg ");
        }

        // 学历职业
        if (member.getEducationLevel() != null) {
            String education = getEducationLabel(member.getEducationLevel());
            profile.append("学历：").append(education).append(" ");
        }
        
        if (member.getOccupation() != null) {
            profile.append("职业：").append(member.getOccupation()).append(" ");
        }
        
        if (member.getMonthlyIncome() != null) {
            String income = getIncomeLabel(member.getMonthlyIncome());
            profile.append("收入：").append(income).append(" ");
        }

        // 婚姻状况
        if (member.getMaritalStatus() != null) {
            String marital = getMaritalStatusLabel(member.getMaritalStatus());
            profile.append("婚姻状况：").append(marital).append(" ");
        }

        // 个人介绍
        if (member.getPersonalIntroduction() != null && !member.getPersonalIntroduction().trim().isEmpty()) {
            profile.append("个人介绍：").append(member.getPersonalIntroduction()).append(" ");
        }

        // 兴趣爱好
        if (member.getHobbies() != null && !member.getHobbies().trim().isEmpty()) {
            profile.append("兴趣爱好：").append(member.getHobbies()).append(" ");
        }

        return profile.toString().trim();
    }

    @Override
    public double calculateCosineSimilarity(List<Double> vector1, List<Double> vector2) {
        if (vector1 == null || vector2 == null || vector1.size() != vector2.size()) {
            return 0.0;
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.size(); i++) {
            double v1 = vector1.get(i);
            double v2 = vector2.get(i);
            
            dotProduct += v1 * v2;
            norm1 += v1 * v1;
            norm2 += v2 * v2;
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    @Override
    public List<Double> batchCalculateSimilarity(List<Double> targetVector, List<List<Double>> candidateVectors) {
        return candidateVectors.stream()
                .map(candidateVector -> calculateCosineSimilarity(targetVector, candidateVector))
                .collect(Collectors.toList());
    }

    @Override
    public boolean updateMemberVector(Member member) {
        try {
            String profileText = generateMemberProfileText(member);
            List<Double> vector = textToVector(profileText);

            MemberEmbedding embedding = new MemberEmbedding();
            embedding.setMemberId(member.getId());
            embedding.setProfileVector(vector);
            embedding.setVectorVersion("v1.0");
            embedding.setUpdateTime(LocalDateTime.now());

            // 使用 MyBatis-Plus 的 saveOrUpdate 方法
            int result = memberEmbeddingMapper.insert(embedding);
            if (result == 0) {
                // 如果插入失败，尝试更新
                result = memberEmbeddingMapper.updateById(embedding);
            }

            log.info("更新会员 {} 的向量成功", member.getId());
            return result > 0;

        } catch (Exception e) {
            log.error("更新会员 {} 向量失败", member.getId(), e);
            return false;
        }
    }

    @Override
    public int batchUpdateMemberVectors(List<Member> members) {
        if (members == null || members.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        List<String> profileTexts = members.stream()
                .map(this::generateMemberProfileText)
                .collect(Collectors.toList());

        try {
            List<List<Double>> vectors = batchTextToVector(profileTexts);
            List<MemberEmbedding> embeddings = new ArrayList<>();

            for (int i = 0; i < members.size(); i++) {
                Member member = members.get(i);
                List<Double> vector = vectors.get(i);

                MemberEmbedding embedding = new MemberEmbedding();
                embedding.setMemberId(member.getId());
                embedding.setProfileVector(vector);
                embedding.setVectorVersion("v1.0");
                embedding.setUpdateTime(LocalDateTime.now());
                embeddings.add(embedding);
            }

            // 批量插入或更新
            successCount = memberEmbeddingMapper.batchInsertOrUpdate(embeddings);
            log.info("批量更新 {} 个会员向量，成功 {} 个", members.size(), successCount);

        } catch (Exception e) {
            log.error("批量更新会员向量失败", e);
        }

        return successCount;
    }

    @Override
    public Map<String, Object> getModelInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("modelName", modelName);
        info.put("dimension", vectorDimension);
        info.put("apiUrl", vectorApiUrl);
        info.put("available", isServiceAvailable());
        return info;
    }

    @Override
    public boolean isServiceAvailable() {
        try {
            List<Double> testVector = textToVector("测试文本");
            return testVector != null && testVector.size() == vectorDimension;
        } catch (Exception e) {
            log.warn("向量化服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 调用向量化API
     */
    private List<List<Double>> callVectorAPI(List<String> texts) throws IOException {
        // 如果向量化服务不可用，返回随机向量（用于演示）
        if (!vectorApiUrl.startsWith("http")) {
            log.warn("向量化服务未配置，使用随机向量");
            return texts.stream()
                    .map(t -> generateRandomVector())
                    .collect(Collectors.toList());
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(vectorApiUrl);
            
            // 构建请求体
            JsonObject requestBody = new JsonObject();
            requestBody.addProperty("model", modelName);
            JsonArray inputArray = new JsonArray();
            texts.forEach(inputArray::add);
            requestBody.add("input", inputArray);

            request.setEntity(new StringEntity(gson.toJson(requestBody), ContentType.APPLICATION_JSON));
            request.setHeader("Content-Type", "application/json");

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                // 读取响应内容 (Java 8兼容)
                InputStream inputStream = response.getEntity().getContent();
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                int nRead;
                byte[] data = new byte[1024];
                while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                    buffer.write(data, 0, nRead);
                }
                String responseBody = new String(buffer.toByteArray(), StandardCharsets.UTF_8);
                
                if (response.getCode() != 200) {
                    throw new IOException("API调用失败: " + response.getCode() + " " + responseBody);
                }

                return parseVectorResponse(responseBody);
            }
        }
    }

    /**
     * 解析向量化API响应
     */
    private List<List<Double>> parseVectorResponse(String responseBody) {
        JsonObject response = gson.fromJson(responseBody, JsonObject.class);
        JsonArray data = response.getAsJsonArray("data");
        
        List<List<Double>> vectors = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JsonObject item = data.get(i).getAsJsonObject();
            JsonArray embedding = item.getAsJsonArray("embedding");
            
            List<Double> vector = new ArrayList<>();
            for (int j = 0; j < embedding.size(); j++) {
                vector.add(embedding.get(j).getAsDouble());
            }
            vectors.add(vector);
        }
        
        return vectors;
    }

    /**
     * 生成零向量
     */
    private List<Double> generateZeroVector() {
        return Collections.nCopies(vectorDimension, 0.0);
    }

    /**
     * 生成随机向量（用于演示）
     */
    private List<Double> generateRandomVector() {
        Random random = new Random();
        List<Double> vector = new ArrayList<>();
        for (int i = 0; i < vectorDimension; i++) {
            vector.add(random.nextGaussian() * 0.1); // 小的随机值
        }
        return vector;
    }

    /**
     * 获取学历标签
     */
    private String getEducationLabel(Integer level) {
        Map<Integer, String> labels = new HashMap<>();
        labels.put(1, "高中");
        labels.put(2, "专科");
        labels.put(3, "本科");
        labels.put(4, "硕士");
        labels.put(5, "博士");
        labels.put(6, "其他");
        return labels.getOrDefault(level, "未知");
    }

    /**
     * 获取收入标签
     */
    private String getIncomeLabel(Integer income) {
        Map<Integer, String> labels = new HashMap<>();
        labels.put(1, "2000以下");
        labels.put(2, "2000-4000");
        labels.put(3, "4000-8000");
        labels.put(4, "8000-20000");
        labels.put(5, "20000以上");
        return labels.getOrDefault(income, "未知");
    }

    /**
     * 获取婚姻状况标签
     */
    private String getMaritalStatusLabel(Integer status) {
        Map<Integer, String> labels = new HashMap<>();
        labels.put(1, "未婚");
        labels.put(2, "离异");
        labels.put(3, "丧偶");
        return labels.getOrDefault(status, "未知");
    }
}
