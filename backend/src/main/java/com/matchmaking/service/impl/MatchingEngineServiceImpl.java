package com.matchmaking.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.matchmaking.dto.MatchRequest;
import com.matchmaking.dto.MatchResult;
import com.matchmaking.entity.*;
import com.matchmaking.mapper.*;
import com.matchmaking.service.ContentGeneratorService;
import com.matchmaking.service.MatchingEngineService;
import com.matchmaking.service.VectorEmbeddingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能匹配引擎服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatchingEngineServiceImpl implements MatchingEngineService {

    private final MemberMapper memberMapper;
    private final MemberEmbeddingMapper memberEmbeddingMapper;
    private final MemberPreferenceMapper memberPreferenceMapper;
    private final MatchRecordMapper matchRecordMapper;
    private final SystemConfigMapper systemConfigMapper;
    private final ContentGeneratorService contentGeneratorService;
    private final VectorEmbeddingService vectorEmbeddingService;

    // 匹配权重配置（从数据库读取，这里设置默认值）
    @Value("${matching.weights.semantic:0.45}")
    private double semanticWeight;

    @Value("${matching.weights.hard-conditions:0.25}")
    private double hardConditionsWeight;

    @Value("${matching.weights.activity:0.15}")
    private double activityWeight;

    @Value("${matching.weights.freshness:0.05}")
    private double freshnessWeight;

    @Value("${matching.weights.matchmaker-priority:0.10}")
    private double matchmakerPriorityWeight;

    @Override
    public List<MatchResult> getMatches(MatchRequest request) {
        log.info("开始为会员 {} 生成匹配推荐", request.getMemberId());

        // 1. 获取目标会员信息
        Member targetMember = memberMapper.selectById(request.getMemberId());
        if (targetMember == null) {
            throw new IllegalArgumentException("会员不存在: " + request.getMemberId());
        }

        // 2. 语义相似度检索候选池
        List<SimilarityResult> semanticCandidates = semanticSimilaritySearch(
                request.getMemberId(), 
                getConfigValue("matching.candidate.pool_size", 100)
        );

        // 3. 获取候选会员详细信息
        List<Long> candidateIds = semanticCandidates.stream()
                .map(SimilarityResult::getMemberId)
                .collect(Collectors.toList());

        if (candidateIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<Member> candidateMembers = memberMapper.selectBatchIds(candidateIds);
        Map<Long, Member> memberMap = candidateMembers.stream()
                .collect(Collectors.toMap(Member::getId, m -> m));

        // 4. 规则过滤
        List<Member> filteredCandidates = ruleFilter(targetMember, candidateMembers, request.getFilter());

        // 5. 综合评分和排序
        List<MatchResult> matchResults = new ArrayList<>();
        for (Member candidate : filteredCandidates) {
            // 获取语义相似度
            double semanticSim = semanticCandidates.stream()
                    .filter(s -> s.getMemberId().equals(candidate.getId()))
                    .mapToDouble(SimilarityResult::getSimilarity)
                    .findFirst()
                    .orElse(0.0);

            // 计算综合分数
            double matchScore = calculateMatchScore(targetMember, candidate, semanticSim);

            // 检查是否已存在匹配记录
            Long existingMatchId = matchRecordMapper.selectExistingMatch(
                    targetMember.getId(), candidate.getId());

            MatchResult result = new MatchResult();
            result.setCandidate(candidate);
            result.setMatchScore(BigDecimal.valueOf(matchScore).setScale(4, RoundingMode.HALF_UP));
            result.setSemanticSimilarity(BigDecimal.valueOf(semanticSim).setScale(4, RoundingMode.HALF_UP));

            if (existingMatchId != null) {
                // 使用已存在的匹配记录
                MatchRecord existingRecord = matchRecordMapper.selectById(existingMatchId);
                result.setMatchId(existingMatchId);
                result.setReason(existingRecord.getReason());
                result.setIceBreakers(existingRecord.getIceBreakers());
                result.setDateSuggestions(existingRecord.getDateSuggestions());
                result.setStatus(existingRecord.getStatus());
            } else {
                // 创建新的匹配记录
                result.setMatchId(null);
                result.setStatus(MatchRecord.Status.RECOMMENDED.getCode());
            }

            matchResults.add(result);
        }

        // 6. 按分数排序并限制数量
        matchResults.sort((a, b) -> b.getMatchScore().compareTo(a.getMatchScore()));
        List<MatchResult> topResults = matchResults.stream()
                .limit(request.getTopK())
                .collect(Collectors.toList());

        // 7. 生成AI内容并保存匹配记录
        if (request.getIncludeAiContent()) {
            generateAiContentAndSaveRecords(targetMember, topResults, request.getMatchmakerId());
        }

        log.info("为会员 {} 生成了 {} 个匹配推荐", request.getMemberId(), topResults.size());
        return topResults;
    }

    @Override
    public double calculateMatchScore(Member memberA, Member memberB) {
        return calculateMatchScore(memberA, memberB, 0.0);
    }

    /**
     * 计算匹配分数（包含语义相似度）
     */
    private double calculateMatchScore(Member memberA, Member memberB, double semanticSimilarity) {
        // 硬性条件匹配度
        double hardScore = calculateHardConditionsScore(memberA, memberB);

        // 活跃度分数
        double activityScore = calculateActivityScore(memberB);

        // 新用户加权
        double freshnessScore = calculateFreshnessScore(memberB);

        // 综合评分
        double finalScore = semanticWeight * semanticSimilarity +
                hardConditionsWeight * hardScore +
                activityWeight * activityScore +
                freshnessWeight * freshnessScore;

        return Math.min(1.0, finalScore);
    }

    /**
     * 硬性条件匹配度计算
     */
    private double calculateHardConditionsScore(Member memberA, Member memberB) {
        double score = 0.0;

        // 年龄匹配 (30%)
        if (memberA.getAge() != null && memberB.getAge() != null) {
            int ageDiff = Math.abs(memberA.getAge() - memberB.getAge());
            if (ageDiff <= 3) score += 0.3;
            else if (ageDiff <= 5) score += 0.2;
            else if (ageDiff <= 8) score += 0.1;
        }

        // 城市匹配 (40%)
        if (Objects.equals(memberA.getCity(), memberB.getCity())) {
            score += 0.4;
        }

        // 学历匹配 (30%)
        if (memberA.getEducationLevel() != null && memberB.getEducationLevel() != null) {
            int eduDiff = Math.abs(memberA.getEducationLevel() - memberB.getEducationLevel());
            if (eduDiff <= 1) score += 0.3;
            else if (eduDiff <= 2) score += 0.15;
        }

        return score;
    }

    /**
     * 活跃度分数计算
     */
    private double calculateActivityScore(Member member) {
        if (member.getLastActiveTime() == null) {
            return 0.5; // 默认分数
        }

        long daysInactive = ChronoUnit.DAYS.between(member.getLastActiveTime(), LocalDateTime.now());
        if (daysInactive <= 7) return 1.0;
        if (daysInactive <= 30) return 0.8;
        if (daysInactive <= 90) return 0.5;
        return 0.2;
    }

    /**
     * 新用户加权分数
     */
    private double calculateFreshnessScore(Member member) {
        if (member.getCreateTime() == null) {
            return 0.5;
        }

        long daysOld = ChronoUnit.DAYS.between(member.getCreateTime(), LocalDateTime.now());
        if (daysOld <= 30) return 1.0; // 新用户
        return 0.5;
    }

    /**
     * 规则过滤
     */
    private List<Member> ruleFilter(Member targetMember, List<Member> candidates, MatchRequest.MatchFilter filter) {
        return candidates.stream()
                .filter(candidate -> {
                    // 排除自己
                    if (candidate.getId().equals(targetMember.getId())) {
                        return false;
                    }

                    // 性别过滤（异性匹配）
                    if (Objects.equals(candidate.getGender(), targetMember.getGender())) {
                        return false;
                    }

                    // 获取目标会员偏好
                    MemberPreference preference = memberPreferenceMapper.selectById(targetMember.getId());
                    if (preference != null && preference.getIsActive() == 1) {
                        // 年龄偏好过滤
                        if (candidate.getAge() != null) {
                            if (preference.getAgeMin() != null && candidate.getAge() < preference.getAgeMin()) {
                                return false;
                            }
                            if (preference.getAgeMax() != null && candidate.getAge() > preference.getAgeMax()) {
                                return false;
                            }
                        }

                        // 身高偏好过滤
                        if (candidate.getHeight() != null) {
                            if (preference.getHeightMin() != null && candidate.getHeight() < preference.getHeightMin()) {
                                return false;
                            }
                            if (preference.getHeightMax() != null && candidate.getHeight() > preference.getHeightMax()) {
                                return false;
                            }
                        }

                        // 学历偏好过滤
                        if (candidate.getEducationLevel() != null && preference.getEducationMin() != null) {
                            if (candidate.getEducationLevel() < preference.getEducationMin()) {
                                return false;
                            }
                        }

                        // 收入偏好过滤
                        if (candidate.getMonthlyIncome() != null && preference.getIncomeMin() != null) {
                            if (candidate.getMonthlyIncome() < preference.getIncomeMin()) {
                                return false;
                            }
                        }

                        // 城市偏好过滤
                        if (preference.getPreferredCities() != null && !preference.getPreferredCities().isEmpty()) {
                            if (!preference.getPreferredCities().contains(candidate.getCity())) {
                                return false;
                            }
                        }
                    }

                    // 请求过滤条件
                    if (filter != null) {
                        if (filter.getExcludeMemberIds() != null && 
                            filter.getExcludeMemberIds().contains(candidate.getId())) {
                            return false;
                        }

                        if (filter.getCity() != null && !filter.getCity().equals(candidate.getCity())) {
                            return false;
                        }

                        if (candidate.getAge() != null) {
                            if (filter.getAgeMin() != null && candidate.getAge() < filter.getAgeMin()) {
                                return false;
                            }
                            if (filter.getAgeMax() != null && candidate.getAge() > filter.getAgeMax()) {
                                return false;
                            }
                        }
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean updateMemberEmbedding(Member member) {
        return vectorEmbeddingService.updateMemberVector(member);
    }

    @Override
    public int batchUpdateMemberEmbeddings(List<Member> members) {
        return vectorEmbeddingService.batchUpdateMemberVectors(members);
    }

    @Override
    public List<SimilarityResult> semanticSimilaritySearch(Long targetMemberId, int candidatePoolSize) {
        try {
            // 1. 获取目标会员的向量
            MemberEmbedding targetEmbedding = memberEmbeddingMapper.selectById(targetMemberId);
            if (targetEmbedding == null || targetEmbedding.getProfileVector() == null) {
                log.warn("目标会员 {} 没有向量数据，尝试生成", targetMemberId);
                Member targetMember = memberMapper.selectById(targetMemberId);
                if (targetMember != null) {
                    vectorEmbeddingService.updateMemberVector(targetMember);
                    targetEmbedding = memberEmbeddingMapper.selectById(targetMemberId);
                }
                if (targetEmbedding == null) {
                    return fallbackSimilaritySearch(targetMemberId, candidatePoolSize);
                }
            }

            // 2. 获取所有候选者的向量
            List<MemberEmbedding> candidateEmbeddings = memberEmbeddingMapper.selectAllVectors("v1.0");
            if (candidateEmbeddings.isEmpty()) {
                return fallbackSimilaritySearch(targetMemberId, candidatePoolSize);
            }

            // 3. 计算相似度
            List<Double> targetVector = targetEmbedding.getProfileVector();
            List<SimilarityResult> results = new ArrayList<>();

            for (MemberEmbedding candidate : candidateEmbeddings) {
                // 排除自己
                if (candidate.getMemberId().equals(targetMemberId)) {
                    continue;
                }

                // 检查会员是否活跃
                Member candidateMember = memberMapper.selectById(candidate.getMemberId());
                if (candidateMember == null || candidateMember.getIsActive() != 1) {
                    continue;
                }

                // 计算相似度
                double similarity = vectorEmbeddingService.calculateCosineSimilarity(
                        targetVector, candidate.getProfileVector());

                results.add(new SimilarityResult(candidate.getMemberId(), similarity));
            }

            // 4. 按相似度排序并限制数量
            return results.stream()
                    .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()))
                    .limit(candidatePoolSize)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("向量相似度检索失败，使用备用方案", e);
            return fallbackSimilaritySearch(targetMemberId, candidatePoolSize);
        }
    }

    /**
     * 备用相似度检索（当向量化服务不可用时）
     */
    private List<SimilarityResult> fallbackSimilaritySearch(Long targetMemberId, int candidatePoolSize) {
        log.info("使用备用相似度检索方案");
        List<Member> allMembers = memberMapper.selectList(
                new QueryWrapper<Member>()
                        .ne("id", targetMemberId)
                        .eq("is_active", 1)
                        .orderByDesc("last_active_time")
                        .last("LIMIT " + candidatePoolSize)
        );

        return allMembers.stream()
                .map(member -> new SimilarityResult(member.getId(), Math.random() * 0.4 + 0.3))
                .collect(Collectors.toList());
    }

    /**
     * 生成AI内容并保存匹配记录
     */
    private void generateAiContentAndSaveRecords(Member targetMember, List<MatchResult> results, Long matchmakerId) {
        for (MatchResult result : results) {
            if (result.getMatchId() == null) {
                // 创建新的匹配记录
                MatchRecord record = new MatchRecord();
                record.setMemberAId(targetMember.getId());
                record.setMemberBId(result.getCandidate().getId());
                record.setMatchmakerId(matchmakerId);
                record.setMatchScore(result.getMatchScore());
                record.setSemanticSimilarity(result.getSemanticSimilarity());
                record.setStatus(MatchRecord.Status.RECOMMENDED.getCode());

                try {
                    // 生成AI内容
                    String reason = contentGeneratorService.generateMatchReason(
                            targetMember, result.getCandidate());
                    List<String> iceBreakers = contentGeneratorService.generateIceBreakers(
                            targetMember, result.getCandidate());

                    record.setReason(reason);
                    record.setIceBreakers(iceBreakers);

                    matchRecordMapper.insert(record);
                    result.setMatchId(record.getId());
                    result.setReason(reason);
                    result.setIceBreakers(iceBreakers);

                } catch (Exception e) {
                    log.error("生成AI内容失败", e);
                    // 即使AI生成失败，也保存基础匹配记录
                    matchRecordMapper.insert(record);
                    result.setMatchId(record.getId());
                }
            }
        }
    }

    /**
     * 获取配置值
     */
    private int getConfigValue(String key, int defaultValue) {
        try {
            SystemConfig config = systemConfigMapper.selectOne(
                    new QueryWrapper<SystemConfig>().eq("config_key", key));
            return config != null ? Integer.parseInt(config.getConfigValue()) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
}
