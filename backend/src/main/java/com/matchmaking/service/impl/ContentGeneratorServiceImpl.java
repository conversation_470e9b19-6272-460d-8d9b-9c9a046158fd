package com.matchmaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.matchmaking.entity.Member;
import com.matchmaking.service.ContentGeneratorService;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * AI内容生成服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class ContentGeneratorServiceImpl implements ContentGeneratorService {

    @Value("${matchmaking.ai.deepseek.api-url}")
    private String deepseekApiUrl;

    @Value("${matchmaking.ai.deepseek.api-key}")
    private String deepseekApiKey;

    @Value("${matchmaking.ai.deepseek.model}")
    private String deepseekModel;

    @Value("${matchmaking.ai.deepseek.timeout:30000}")
    private int timeout;

    @Value("${matchmaking.ai.deepseek.max-tokens:1000}")
    private int maxTokens;

    private ChatLanguageModel chatModel;

    @PostConstruct
    public void init() {
        try {
            // 初始化LangChain4j的OpenAI兼容模型（DeepSeek API兼容OpenAI格式）
            this.chatModel = OpenAiChatModel.builder()
                    .baseUrl(deepseekApiUrl)
                    .apiKey(deepseekApiKey)
                    .modelName(deepseekModel)
                    .timeout(Duration.ofMillis(timeout))
                    .maxTokens(maxTokens)
                    .temperature(0.7) // 内容生成需要更高的创造性
                    .build();
            
            log.info("AI内容生成服务初始化成功，使用模型: {}", deepseekModel);
        } catch (Exception e) {
            log.error("AI内容生成服务初始化失败", e);
            this.chatModel = null;
        }
    }

    @Override
    public String generateMatchReason(Member memberA, Member memberB) {
        if (!isAvailable()) {
            return "AI服务暂不可用，请联系管理员";
        }

        try {
            String memberAInfo = formatMemberInfo(memberA);
            String memberBInfo = formatMemberInfo(memberB);
            
            return generateMatchReasonWithAnnotations(memberAInfo, memberBInfo);
            
        } catch (Exception e) {
            log.error("生成匹配推荐理由失败", e);
            return "推荐理由生成失败，请稍后重试";
        }
    }

    @Override
    public List<String> generateIceBreakers(Member memberA, Member memberB) {
        if (!isAvailable()) {
            return Arrays.asList("AI服务暂不可用", "请联系管理员", "稍后重试");
        }

        try {
            String targetMemberInfo = formatMemberInfo(memberB);
            String response = generateIceBreakersWithAnnotations(targetMemberInfo);
            
            // 解析JSON响应
            JSONObject jsonResponse = JSON.parseObject(response);
            JSONArray iceBreakersArray = jsonResponse.getJSONArray("iceBreakers");
            
            List<String> iceBreakers = new ArrayList<>();
            if (iceBreakersArray != null) {
                for (int i = 0; i < iceBreakersArray.size(); i++) {
                    iceBreakers.add(iceBreakersArray.getString(i));
                }
            }
            
            return iceBreakers.isEmpty() ? Arrays.asList("开场白生成失败", "请稍后重试") : iceBreakers;
            
        } catch (Exception e) {
            log.error("生成破冰开场白失败", e);
            return Arrays.asList("开场白生成失败", "请稍后重试");
        }
    }

    @Override
    public List<String> generateDateSuggestions(Member memberA, Member memberB, String city) {
        if (!isAvailable()) {
            return Arrays.asList("AI服务暂不可用", "请联系管理员");
        }

        try {
            String memberAInfo = extractHobbiesAndInterests(memberA);
            String memberBInfo = extractHobbiesAndInterests(memberB);
            
            String response = generateDateSuggestionsWithAnnotations(memberAInfo, memberBInfo, city);
            
            // 解析JSON响应
            JSONObject jsonResponse = JSON.parseObject(response);
            JSONArray suggestionsArray = jsonResponse.getJSONArray("suggestions");
            
            List<String> suggestions = new ArrayList<>();
            if (suggestionsArray != null) {
                for (int i = 0; i < suggestionsArray.size(); i++) {
                    suggestions.add(suggestionsArray.getString(i));
                }
            }
            
            return suggestions.isEmpty() ? Arrays.asList("约会建议生成失败", "请稍后重试") : suggestions;
            
        } catch (Exception e) {
            log.error("生成约会建议失败", e);
            return Arrays.asList("约会建议生成失败", "请稍后重试");
        }
    }

    @Override
    public String generateMatchReasonWithAnnotations(String memberAInfo, String memberBInfo) {
        if (!isAvailable()) {
            return "AI服务不可用";
        }

        try {
            String prompt = "请分析这两位会员的匹配度并给出推荐理由：\n\n" +
                           "会员A：" + memberAInfo + "\n\n" +
                           "会员B：" + memberBInfo + "\n\n" +
                           "请从以下角度分析（150字以内）：\n" +
                           "1. 3个互补优势点\n" +
                           "2. 2个共同点\n" +
                           "3. 1个需要注意的潜在问题\n" +
                           "格式：简洁明了，适合红娘向客户介绍";
            
            return chatModel.generate(prompt);
            
        } catch (Exception e) {
            log.error("注解式匹配理由生成失败", e);
            return "推荐理由生成异常: " + e.getMessage();
        }
    }

    @Override
    public String generateIceBreakersWithAnnotations(String targetMemberInfo) {
        if (!isAvailable()) {
            return "{\"iceBreakers\": [\"AI服务不可用\", \"请联系管理员\", \"稍后重试\"]}";
        }

        try {
            String prompt = "请为客户生成3条不同风格的微信开场白：\n\n" +
                           "对方信息：" + targetMemberInfo + "\n\n" +
                           "要求：\n" +
                           "1. 每条50-80字\n" +
                           "2. 三种风格：温和询问型、共同话题型、轻松幽默型\n" +
                           "3. 避免太直接的外貌或收入相关表达\n" +
                           "4. 自然真诚，不要太刻意\n\n" +
                           "请返回JSON格式：{\"iceBreakers\": [\"开场白1\", \"开场白2\", \"开场白3\"]}";
            
            return chatModel.generate(prompt);
            
        } catch (Exception e) {
            log.error("注解式开场白生成失败", e);
            return "{\"iceBreakers\": [\"开场白生成异常\", \"请稍后重试\", \"联系管理员\"]}";
        }
    }

    @Override
    public String generateDateSuggestionsWithAnnotations(String memberAInfo, String memberBInfo, String city) {
        if (!isAvailable()) {
            return "{\"suggestions\": [\"AI服务不可用\", \"请联系管理员\"]}";
        }

        try {
            String prompt = "为这对配对会员推荐3个" + city + "的约会地点和活动：\n\n" +
                           "会员A兴趣：" + memberAInfo + "\n" +
                           "会员B兴趣：" + memberBInfo + "\n\n" +
                           "要求：\n" +
                           "1. 考虑双方兴趣交集\n" +
                           "2. 适合初次见面（公共场所）\n" +
                           "3. 包含具体地点名称\n" +
                           "4. 每条30-50字\n\n" +
                           "请返回JSON格式：{\"suggestions\": [\"地点1 - 活动描述\", \"地点2 - 活动描述\", \"地点3 - 活动描述\"]}";
            
            return chatModel.generate(prompt);
            
        } catch (Exception e) {
            log.error("注解式约会建议生成失败", e);
            return "{\"suggestions\": [\"约会建议生成异常\", \"请稍后重试\"]}";
        }
    }

    @Override
    public String generateProfileOptimization(String currentProfile, String memberInfo) {
        if (!isAvailable()) {
            return "AI服务不可用，无法提供优化建议";
        }

        try {
            String prompt = "请为这位会员的个人介绍提供优化建议：\n\n" +
                           "当前介绍：" + currentProfile + "\n" +
                           "会员信息：" + memberInfo + "\n\n" +
                           "请提供：\n" +
                           "1. 优化后的个人介绍（100-150字）\n" +
                           "2. 3个改进要点\n" +
                           "3. 建议突出的个人亮点\n\n" +
                           "要求：真实、积极、有吸引力";
            
            return chatModel.generate(prompt);
            
        } catch (Exception e) {
            log.error("个人介绍优化失败", e);
            return "优化建议生成异常: " + e.getMessage();
        }
    }

    @Override
    public boolean isAvailable() {
        return chatModel != null;
    }

    /**
     * 格式化会员信息为文本描述
     */
    private String formatMemberInfo(Member member) {
        StringBuilder info = new StringBuilder();
        info.append(member.getName()).append("，")
            .append(member.getAge()).append("岁，")
            .append(member.getCity()).append("，")
            .append(getEducationText(member.getEducationLevel())).append("，")
            .append(member.getOccupation());
        
        if (member.getHobbies() != null && !member.getHobbies().trim().isEmpty()) {
            info.append("，兴趣：").append(member.getHobbies());
        }
        
        if (member.getSelfDescription() != null && !member.getSelfDescription().trim().isEmpty()) {
            info.append("，个人介绍：").append(member.getSelfDescription());
        }
        
        return info.toString();
    }

    /**
     * 提取会员的兴趣爱好信息
     */
    private String extractHobbiesAndInterests(Member member) {
        StringBuilder interests = new StringBuilder();
        
        if (member.getHobbies() != null && !member.getHobbies().trim().isEmpty()) {
            interests.append(member.getHobbies());
        }
        
        if (member.getSelfDescription() != null && !member.getSelfDescription().trim().isEmpty()) {
            if (interests.length() > 0) {
                interests.append("，");
            }
            interests.append(member.getSelfDescription());
        }
        
        return interests.length() > 0 ? interests.toString() : "暂无特殊兴趣爱好";
    }

    /**
     * 将学历代码转换为文本
     */
    private String getEducationText(Integer educationLevel) {
        if (educationLevel == null) return "未知学历";
        
        switch (educationLevel) {
            case 1: return "高中";
            case 2: return "专科";
            case 3: return "本科";
            case 4: return "硕士";
            case 5: return "博士";
            case 6: return "其他";
            default: return "未知学历";
        }
    }
}
