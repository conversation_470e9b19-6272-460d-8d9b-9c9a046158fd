package com.matchmaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.matchmaking.dto.OcrResultDTO;
import com.matchmaking.service.AiCorrectionService;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI纠错服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AiCorrectionServiceImpl implements AiCorrectionService {

    @Value("${matchmaking.ai.deepseek.api-url}")
    private String deepseekApiUrl;

    @Value("${matchmaking.ai.deepseek.api-key}")
    private String deepseekApiKey;

    @Value("${matchmaking.ai.deepseek.model}")
    private String deepseekModel;

    @Value("${matchmaking.ai.deepseek.timeout:30000}")
    private int timeout;

    @Value("${matchmaking.ai.deepseek.max-tokens:1000}")
    private int maxTokens;

    private ChatLanguageModel chatModel;

    @PostConstruct
    public void init() {
        try {
            // 初始化LangChain4j的OpenAI兼容模型（DeepSeek API兼容OpenAI格式）
            this.chatModel = OpenAiChatModel.builder()
                    .baseUrl(deepseekApiUrl)
                    .apiKey(deepseekApiKey)
                    .modelName(deepseekModel)
                    .timeout(Duration.ofMillis(timeout))
                    .maxTokens(maxTokens)
                    .temperature(0.1) // 低温度，减少随机性
                    .build();
            
            log.info("AI纠错服务初始化成功，使用模型: {}", deepseekModel);
        } catch (Exception e) {
            log.error("AI纠错服务初始化失败", e);
            this.chatModel = null;
        }
    }

    @Override
    public Map<String, Object> correctErrors(Map<String, Object> originalFields, 
                                           List<OcrResultDTO.ValidationError> validationErrors,
                                           String rawOcrText) {
        if (!isAvailable() || validationErrors.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 使用注解方法进行AI纠错
            String ocrDataJson = JSON.toJSONString(originalFields);
            String errorsJson = JSON.toJSONString(validationErrors);

            String response = correctDataWithAnnotations(ocrDataJson, errorsJson);

            // 解析响应
            Map<String, Object> aiResult = parseAiResponse(response, validationErrors);

            // 如果响应包含corrections字段，提取它
            if (aiResult.containsKey("corrections") && aiResult.get("corrections") instanceof Map) {
                return (Map<String, Object>) aiResult.get("corrections");
            }

            return aiResult;

        } catch (Exception e) {
            log.error("AI纠错处理失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Object correctSingleField(String fieldName, Object originalValue, 
                                   OcrResultDTO.ValidationError validationError,
                                   Map<String, Object> context) {
        if (!isAvailable()) {
            return null;
        }

        try {
            // 构建单字段纠错提示词
            String prompt = buildSingleFieldPrompt(fieldName, originalValue, validationError, context);
            
            // 调用AI模型
            String response = chatModel.generate(prompt);
            
            // 解析单字段响应
            return parseSingleFieldResponse(response, fieldName);
            
        } catch (Exception e) {
            log.error("单字段AI纠错失败: {}", fieldName, e);
            return null;
        }
    }

    @Override
    public String correctDataWithAnnotations(String ocrData, String validationErrors) {
        if (!isAvailable()) {
            return "{\"corrections\": {}, \"explanation\": \"AI服务不可用\", \"confidence\": 0.0}";
        }

        try {
            // 构建提示词
            String prompt = "请帮我分析以下OCR识别的婚介登记表数据，并针对校验错误提供修正建议：\n\n" +
                           "OCR识别结果：" + ocrData + "\n\n" +
                           "校验错误信息：" + validationErrors + "\n\n" +
                           "请返回JSON格式的修正建议，包含：\n" +
                           "1. corrections: 建议的修正值 (字段名->修正值)\n" +
                           "2. explanation: 修正原因说明\n" +
                           "3. confidence: 修正置信度(0-1)\n\n" +
                           "注意：只对明显的OCR识别错误进行修正，对于合理范围内的数据不要修改。";

            return chatModel.generate(prompt);

        } catch (Exception e) {
            log.error("注解式AI纠错处理失败", e);
            return "{\"corrections\": {}, \"explanation\": \"处理异常: " + e.getMessage() + "\", \"confidence\": 0.0}";
        }
    }

    @Override
    public boolean isAvailable() {
        return chatModel != null;
    }

    /**
     * 构建批量纠错提示词
     */
    private String buildCorrectionPrompt(Map<String, Object> originalFields, 
                                       List<OcrResultDTO.ValidationError> validationErrors,
                                       String rawOcrText) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个专业的婚介所数据纠错专家。请根据以下信息纠正OCR识别错误：\n\n");
        
        // 原始识别结果
        prompt.append("【原始OCR识别结果】\n");
        prompt.append(JSON.toJSONString(originalFields, true)).append("\n\n");
        
        // 校验错误
        prompt.append("【发现的错误】\n");
        for (int i = 0; i < validationErrors.size(); i++) {
            OcrResultDTO.ValidationError error = validationErrors.get(i);
            prompt.append(String.format("%d. 字段'%s': %s (原值: %s)\n", 
                i + 1, error.getFieldName(), error.getErrorMessage(), error.getOriginalValue()));
        }
        prompt.append("\n");
        
        // 字段说明
        prompt.append("【字段说明】\n");
        prompt.append("- gender: 1=男, 2=女\n");
        prompt.append("- educationLevel: 1=高中, 2=专科, 3=本科, 4=硕士, 5=博士, 6=其他\n");
        prompt.append("- marriageStatus: 1=未婚, 2=离异, 3=丧偶\n");
        prompt.append("- monthlyIncome: 1=2000以下, 2=2000-4000, 3=4000-8000, 4=8000-20000, 5=20000以上\n");
        prompt.append("- companyType: 1=国企, 2=外企, 3=私企, 4=事业单位, 5=公务员, 6=自由职业\n");
        prompt.append("- houseStatus: 1=有房, 2=无房, 3=按揭\n");
        prompt.append("- carStatus: 1=有车, 2=无车\n");
        prompt.append("- age: 18-80岁\n");
        prompt.append("- height: 140-220cm\n");
        prompt.append("- weight: 35-200kg\n\n");
        
        // 纠错要求
        prompt.append("【纠错要求】\n");
        prompt.append("1. 仅纠正有错误的字段\n");
        prompt.append("2. 基于常识和上下文推断正确值\n");
        prompt.append("3. 数字字段返回数字类型\n");
        prompt.append("4. 如果无法确定正确值，返回null\n");
        prompt.append("5. 返回JSON格式，只包含需要纠正的字段\n\n");
        
        prompt.append("请返回纠正后的字段JSON（只包含需要修改的字段）：");
        
        return prompt.toString();
    }

    /**
     * 构建单字段纠错提示词
     */
    private String buildSingleFieldPrompt(String fieldName, Object originalValue, 
                                        OcrResultDTO.ValidationError validationError,
                                        Map<String, Object> context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请纠正以下婚介所表格字段的识别错误：\n\n");
        
        prompt.append("【字段信息】\n");
        prompt.append("字段名: ").append(fieldName).append("\n");
        prompt.append("原始值: ").append(originalValue).append("\n");
        prompt.append("错误类型: ").append(validationError.getErrorType()).append("\n");
        prompt.append("错误描述: ").append(validationError.getErrorMessage()).append("\n\n");
        
        // 添加字段规则说明
        prompt.append("【字段规则】\n");
        prompt.append(getFieldRuleDescription(fieldName)).append("\n\n");
        
        // 添加上下文
        if (context != null && !context.isEmpty()) {
            prompt.append("【相关上下文】\n");
            context.forEach((key, value) -> {
                if (!key.equals(fieldName)) {
                    prompt.append(key).append(": ").append(value).append("\n");
                }
            });
            prompt.append("\n");
        }
        
        prompt.append("请返回纠正后的值（如果无法确定，返回null）：");
        
        return prompt.toString();
    }

    /**
     * 解析AI批量纠错响应
     */
    private Map<String, Object> parseAiResponse(String response, List<OcrResultDTO.ValidationError> validationErrors) {
        try {
            // 尝试解析JSON
            String jsonStr = extractJsonFromResponse(response);
            if (jsonStr != null) {
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                Map<String, Object> corrections = new HashMap<>();
                
                // 只保留有错误的字段的纠正结果
                for (OcrResultDTO.ValidationError error : validationErrors) {
                    String fieldName = error.getFieldName();
                    if (jsonObject.containsKey(fieldName)) {
                        Object correctedValue = jsonObject.get(fieldName);
                        if (correctedValue != null) {
                            corrections.put(fieldName, correctedValue);
                        }
                    }
                }
                
                return corrections;
            }
        } catch (Exception e) {
            log.warn("解析AI纠错响应失败: {}", response, e);
        }
        
        return new HashMap<>();
    }

    /**
     * 解析单字段AI响应
     */
    private Object parseSingleFieldResponse(String response, String fieldName) {
        try {
            // 清理响应文本
            String cleanResponse = response.trim();
            
            // 如果是null或空
            if ("null".equalsIgnoreCase(cleanResponse) || cleanResponse.isEmpty()) {
                return null;
            }
            
            // 尝试解析为数字（针对数字字段）
            if (isNumericField(fieldName)) {
                try {
                    return Integer.parseInt(cleanResponse.replaceAll("[^0-9]", ""));
                } catch (NumberFormatException e) {
                    log.warn("无法解析数字字段{}: {}", fieldName, cleanResponse);
                    return null;
                }
            }
            
            // 返回字符串值
            return cleanResponse;
            
        } catch (Exception e) {
            log.warn("解析单字段AI响应失败: {}", response, e);
            return null;
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON块
        int startIndex = response.indexOf('{');
        int endIndex = response.lastIndexOf('}');
        
        if (startIndex >= 0 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }
        
        return null;
    }

    /**
     * 判断是否为数字字段
     */
    private boolean isNumericField(String fieldName) {
        return fieldName.equals("age") || fieldName.equals("height") || fieldName.equals("weight") ||
               fieldName.equals("gender") || fieldName.equals("educationLevel") || 
               fieldName.equals("monthlyIncome") || fieldName.equals("marriageStatus") ||
               fieldName.equals("companyType") || fieldName.equals("houseStatus") ||
               fieldName.equals("carStatus") || fieldName.startsWith("preferred");
    }

    /**
     * 获取字段规则描述
     */
    private String getFieldRuleDescription(String fieldName) {
        switch (fieldName) {
            case "gender":
                return "性别：1=男，2=女";
            case "age":
                return "年龄：18-80岁之间的整数";
            case "height":
                return "身高：140-220cm之间的整数";
            case "weight":
                return "体重：35-200kg之间的整数";
            case "educationLevel":
                return "学历：1=高中，2=专科，3=本科，4=硕士，5=博士，6=其他";
            case "marriageStatus":
                return "婚姻状况：1=未婚，2=离异，3=丧偶";
            case "monthlyIncome":
                return "月收入：1=2000以下，2=2000-4000，3=4000-8000，4=8000-20000，5=20000以上";
            case "companyType":
                return "单位性质：1=国企，2=外企，3=私企，4=事业单位，5=公务员，6=自由职业";
            case "houseStatus":
                return "房产情况：1=有房，2=无房，3=按揭";
            case "carStatus":
                return "车产情况：1=有车，2=无车";
            case "phone":
                return "手机号：11位数字，以1开头";
            case "birthDate":
                return "出生日期：YYYY-MM或YYYY-MM-DD格式";
            default:
                return "文本字段，无特殊限制";
        }
    }
}
