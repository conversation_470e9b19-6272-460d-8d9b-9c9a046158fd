package com.matchmaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.matchmaking.dto.OcrResultDTO;
import com.matchmaking.service.OcrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * OCR识别服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class OcrServiceImpl implements OcrService {

    @Value("${matchmaking.ocr.qwen.api-url}")
    private String qwenApiUrl;

    @Value("${matchmaking.ocr.qwen.api-key}")
    private String qwenApiKey;

    @Value("${matchmaking.ocr.qwen.model}")
    private String qwenModel;

    @Resource
    private RestTemplate restTemplate;

    @Override
    public OcrResultDTO recognizeForm(MultipartFile imageFile) {
        try {
            // 将图片转换为Base64
            byte[] imageBytes = imageFile.getBytes();
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            
            return callQwenVLApi(base64Image);
        } catch (Exception e) {
            log.error("OCR识别失败", e);
            throw new RuntimeException("OCR识别失败: " + e.getMessage());
        }
    }

    @Override
    public OcrResultDTO recognizeForm(String imageUrl) {
        try {
            return callQwenVLApiWithUrl(imageUrl);
        } catch (Exception e) {
            log.error("OCR识别失败", e);
            throw new RuntimeException("OCR识别失败: " + e.getMessage());
        }
    }

    /**
     * 调用Qwen3-VL API（Base64图片）
     */
    private OcrResultDTO callQwenVLApi(String base64Image) {
        // 构建请求体
        Map<String, Object> requestBody = buildQwenRequest(base64Image, null);
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + qwenApiKey);
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            // 调用API
            ResponseEntity<String> response = restTemplate.postForEntity(qwenApiUrl, request, String.class);
            
            // 解析响应
            return parseQwenResponse(response.getBody());
        } catch (Exception e) {
            log.error("调用Qwen3-VL API失败", e);
            throw new RuntimeException("调用OCR API失败: " + e.getMessage());
        }
    }

    /**
     * 调用Qwen3-VL API（图片URL）
     */
    private OcrResultDTO callQwenVLApiWithUrl(String imageUrl) {
        // 构建请求体
        Map<String, Object> requestBody = buildQwenRequest(null, imageUrl);
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + qwenApiKey);
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            // 调用API
            ResponseEntity<String> response = restTemplate.postForEntity(qwenApiUrl, request, String.class);
            
            // 解析响应
            return parseQwenResponse(response.getBody());
        } catch (Exception e) {
            log.error("调用Qwen3-VL API失败", e);
            throw new RuntimeException("调用OCR API失败: " + e.getMessage());
        }
    }

    /**
     * 构建Qwen请求体
     */
    private Map<String, Object> buildQwenRequest(String base64Image, String imageUrl) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", qwenModel);
        
        // 构建消息
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        
        // 构建内容
        Map<String, Object> content = new HashMap<>();
        if (base64Image != null) {
            content.put("image", "data:image/jpeg;base64," + base64Image);
        } else if (imageUrl != null) {
            content.put("image", imageUrl);
        }
        
        // 设置提示词 - 专门用于婚介所表格识别
        String prompt = """
            请识别这张婚介所会员登记表格，提取以下字段信息并返回JSON格式：
            {
              "name": "姓名",
              "gender": "性别(1-男,2-女)",
              "birthDate": "出生年月(YYYY-MM格式)",
              "age": "年龄(数字)",
              "ethnicity": "民族",
              "height": "身高(cm,数字)",
              "weight": "体重(kg,数字)",
              "educationLevel": "学历(1-高中,2-专科,3-本科,4-硕士,5-博士,6-其他)",
              "major": "专业",
              "occupation": "职业",
              "companyType": "单位性质(1-国企,2-外企,3-私企,4-事业单位,5-公务员,6-自由职业)",
              "monthlyIncome": "月收入(1-2000以下,2-2000-4000,3-4000-8000,4-8000-20000,5-20000以上)",
              "marriageStatus": "婚姻状况(1-未婚,2-离异,3-丧偶)",
              "houseStatus": "房产情况(1-有房,2-无房,3-按揭)",
              "carStatus": "车产情况(1-有车,2-无车)",
              "parentsOccupation": "父母职业",
              "siblings": "兄弟姐妹情况",
              "preferredAgeMin": "期望年龄最小值",
              "preferredAgeMax": "期望年龄最大值",
              "preferredEducation": "期望学历",
              "preferredHeightMin": "期望身高最小值",
              "preferredMarriageStatus": "期望婚姻状况",
              "hobbies": "兴趣爱好",
              "valuesNotes": "价值观/备注",
              "phone": "手机号",
              "wechat": "微信号"
            }
            
            注意：
            1. 如果某个字段在表格中没有或无法识别，请设置为null
            2. 数字字段请返回数字类型，不要返回字符串
            3. 枚举字段请返回对应的数字编码
            4. 只返回JSON，不要其他说明文字
            """;
        
        content.put("text", prompt);
        message.put("content", content);
        
        requestBody.put("messages", new Object[]{message});
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.1);
        
        return requestBody;
    }

    /**
     * 解析Qwen响应
     */
    private OcrResultDTO parseQwenResponse(String responseBody) {
        try {
            JSONObject response = JSON.parseObject(responseBody);
            
            OcrResultDTO result = new OcrResultDTO();
            result.setRawResponse(responseBody);
            
            // 提取识别结果
            JSONObject choices = response.getJSONArray("choices").getJSONObject(0);
            String content = choices.getJSONObject("message").getString("content");
            
            // 解析JSON内容
            try {
                JSONObject fields = JSON.parseObject(content);
                result.setFields(fields);
                result.setConfidence(new BigDecimal("0.85")); // 模拟置信度
            } catch (Exception e) {
                log.warn("解析OCR结果JSON失败，使用原始文本: {}", content);
                // 如果解析失败，尝试从文本中提取信息
                Map<String, Object> fields = extractFieldsFromText(content);
                result.setFields(fields);
                result.setConfidence(new BigDecimal("0.60")); // 降低置信度
            }
            
            return result;
        } catch (Exception e) {
            log.error("解析Qwen响应失败", e);
            throw new RuntimeException("解析OCR响应失败: " + e.getMessage());
        }
    }

    /**
     * 从文本中提取字段信息（备用方案）
     */
    private Map<String, Object> extractFieldsFromText(String text) {
        Map<String, Object> fields = new HashMap<>();
        
        // 这里可以实现简单的文本解析逻辑
        // 作为OCR JSON解析失败时的备用方案
        
        // 示例：提取姓名
        if (text.contains("姓名")) {
            // 简单的正则匹配逻辑
        }
        
        return fields;
    }
}
