package com.matchmaking.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.matchmaking.dto.OcrResultDTO;
import com.matchmaking.entity.Member;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 会员信息服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface MemberService extends IService<Member> {

    /**
     * 处理表格图片上传和OCR识别
     * 
     * @param imageFile 图片文件
     * @param operator 操作人
     * @return OCR识别结果
     */
    OcrResultDTO processFormImage(MultipartFile imageFile, String operator);

    /**
     * 确认并保存会员信息
     * 
     * @param fields 确认后的字段数据
     * @param rawOcrDataId 原始OCR数据ID
     * @param operator 操作人
     * @return 保存的会员信息
     */
    Member confirmAndSaveMember(Map<String, Object> fields, Long rawOcrDataId, String operator);

    /**
     * 记录审计日志
     * 
     * @param memberId 会员ID
     * @param fieldName 字段名
     * @param oldValue 原值
     * @param newValue 新值
     * @param operationType 操作类型
     * @param operator 操作人
     * @param operatorType 操作人类型
     * @param remark 备注
     */
    void recordAuditLog(Long memberId, String fieldName, String oldValue, String newValue,
                       Integer operationType, String operator, Integer operatorType, String remark);
}
