package com.matchmaking.service;

import com.matchmaking.entity.Member;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

import java.util.List;

/**
 * AI内容生成服务接口
 * 为婚介所提供智能内容生成功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ContentGeneratorService {

    /**
     * 生成匹配推荐理由
     * 
     * @param memberA 会员A
     * @param memberB 会员B
     * @return 推荐理由文本
     */
    String generateMatchReason(Member memberA, Member memberB);

    /**
     * 生成破冰开场白
     * 
     * @param memberA 发起方会员
     * @param memberB 目标会员
     * @return 开场白列表（通常3条不同风格）
     */
    List<String> generateIceBreakers(Member memberA, Member memberB);

    /**
     * 生成约会建议
     * 
     * @param memberA 会员A
     * @param memberB 会员B
     * @param city 约会城市
     * @return 约会建议列表
     */
    List<String> generateDateSuggestions(Member memberA, Member memberB, String city);

    /**
     * 使用LangChain4j注解生成匹配推荐理由
     * 
     * @param memberAInfo 会员A信息
     * @param memberBInfo 会员B信息
     * @return 推荐理由
     */
    @SystemMessage("你是一位经验丰富的专业红娘，擅长分析两个人的匹配度并给出专业的推荐理由。")
    @UserMessage("请分析这两位会员的匹配度并给出推荐理由：\n\n" +
                "会员A：{{memberAInfo}}\n\n" +
                "会员B：{{memberBInfo}}\n\n" +
                "请从以下角度分析（150字以内）：\n" +
                "1. 3个互补优势点\n" +
                "2. 2个共同点\n" +
                "3. 1个需要注意的潜在问题\n" +
                "格式：简洁明了，适合红娘向客户介绍")
    String generateMatchReasonWithAnnotations(@V("memberAInfo") String memberAInfo,
                                             @V("memberBInfo") String memberBInfo);

    /**
     * 使用LangChain4j注解生成破冰开场白
     * 
     * @param targetMemberInfo 目标会员信息
     * @return 开场白建议（JSON格式）
     */
    @SystemMessage("你是一位专业的情感咨询师，擅长帮助客户开始自然真诚的对话。")
    @UserMessage("请为客户生成3条不同风格的微信开场白：\n\n" +
                "对方信息：{{targetMemberInfo}}\n\n" +
                "要求：\n" +
                "1. 每条50-80字\n" +
                "2. 三种风格：温和询问型、共同话题型、轻松幽默型\n" +
                "3. 避免太直接的外貌或收入相关表达\n" +
                "4. 自然真诚，不要太刻意\n\n" +
                "请返回JSON格式：{\"iceBreakers\": [\"开场白1\", \"开场白2\", \"开场白3\"]}")
    String generateIceBreakersWithAnnotations(@V("targetMemberInfo") String targetMemberInfo);

    /**
     * 使用LangChain4j注解生成约会建议
     * 
     * @param memberAInfo 会员A信息
     * @param memberBInfo 会员B信息
     * @param city 约会城市
     * @return 约会建议（JSON格式）
     */
    @SystemMessage("你是一位专业的约会规划师，熟悉各个城市的约会场所和活动。")
    @UserMessage("为这对配对会员推荐3个{{city}}的约会地点和活动：\n\n" +
                "会员A兴趣：{{memberAInfo}}\n" +
                "会员B兴趣：{{memberBInfo}}\n\n" +
                "要求：\n" +
                "1. 考虑双方兴趣交集\n" +
                "2. 适合初次见面（公共场所）\n" +
                "3. 包含具体地点名称\n" +
                "4. 每条30-50字\n\n" +
                "请返回JSON格式：{\"suggestions\": [\"地点1 - 活动描述\", \"地点2 - 活动描述\", \"地点3 - 活动描述\"]}")
    String generateDateSuggestionsWithAnnotations(@V("memberAInfo") String memberAInfo,
                                                 @V("memberBInfo") String memberBInfo,
                                                 @V("city") String city);

    /**
     * 生成会员个人介绍优化建议
     * 
     * @param member 会员信息
     * @return 优化建议
     */
    @SystemMessage("你是一位专业的个人形象顾问，擅长帮助客户优化个人介绍。")
    @UserMessage("请为这位会员的个人介绍提供优化建议：\n\n" +
                "当前介绍：{{currentProfile}}\n" +
                "会员信息：{{memberInfo}}\n\n" +
                "请提供：\n" +
                "1. 优化后的个人介绍（100-150字）\n" +
                "2. 3个改进要点\n" +
                "3. 建议突出的个人亮点\n\n" +
                "要求：真实、积极、有吸引力")
    String generateProfileOptimization(@V("currentProfile") String currentProfile,
                                      @V("memberInfo") String memberInfo);

    /**
     * 检查内容生成服务是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();
}
