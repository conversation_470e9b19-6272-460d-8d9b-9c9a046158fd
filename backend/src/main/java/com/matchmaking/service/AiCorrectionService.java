package com.matchmaking.service;

import com.matchmaking.dto.OcrResultDTO;

import java.util.List;
import java.util.Map;

/**
 * AI纠错服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AiCorrectionService {

    /**
     * AI纠错处理
     * 
     * @param originalFields 原始OCR识别字段
     * @param validationErrors 校验错误列表
     * @param rawOcrText 原始OCR文本（用于上下文）
     * @return 纠错后的字段建议
     */
    Map<String, Object> correctErrors(Map<String, Object> originalFields, 
                                     List<OcrResultDTO.ValidationError> validationErrors,
                                     String rawOcrText);

    /**
     * 单字段AI纠错
     * 
     * @param fieldName 字段名
     * @param originalValue 原始值
     * @param validationError 校验错误
     * @param context 上下文信息
     * @return 纠错建议值
     */
    Object correctSingleField(String fieldName, Object originalValue, 
                             OcrResultDTO.ValidationError validationError,
                             Map<String, Object> context);

    /**
     * 检查AI纠错是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();
}
