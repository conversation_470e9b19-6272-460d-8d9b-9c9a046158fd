package com.matchmaking.service;

import com.matchmaking.dto.OcrResultDTO;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

import java.util.List;
import java.util.Map;

/**
 * AI纠错服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AiCorrectionService {

    /**
     * AI纠错处理
     *
     * @param originalFields 原始OCR识别字段
     * @param validationErrors 校验错误列表
     * @param rawOcrText 原始OCR文本（用于上下文）
     * @return 纠错后的字段建议
     */
    Map<String, Object> correctErrors(Map<String, Object> originalFields,
                                     List<OcrResultDTO.ValidationError> validationErrors,
                                     String rawOcrText);

    /**
     * 单字段AI纠错
     *
     * @param fieldName 字段名
     * @param originalValue 原始值
     * @param validationError 校验错误
     * @param context 上下文信息
     * @return 纠错建议值
     */
    Object correctSingleField(String fieldName, Object originalValue,
                             OcrResultDTO.ValidationError validationError,
                             Map<String, Object> context);

    /**
     * 使用LangChain4j注解的AI纠错方法
     *
     * @param ocrData OCR识别的原始数据
     * @param validationErrors 校验错误信息
     * @return JSON格式的纠错建议
     */
    @SystemMessage("你是一个专业的婚介所数据校验助手。请根据用户提供的OCR识别结果和校验错误信息，提供合理的修正建议。")
    @UserMessage("请帮我分析以下OCR识别的婚介登记表数据，并针对校验错误提供修正建议：\n\n" +
                "OCR识别结果：{{ocrData}}\n\n" +
                "校验错误信息：{{validationErrors}}\n\n" +
                "请返回JSON格式的修正建议，包含：\n" +
                "1. corrections: 建议的修正值 (字段名->修正值)\n" +
                "2. explanation: 修正原因说明\n" +
                "3. confidence: 修正置信度(0-1)\n\n" +
                "注意：只对明显的OCR识别错误进行修正，对于合理范围内的数据不要修改。")
    String correctDataWithAnnotations(@V("ocrData") String ocrData,
                                     @V("validationErrors") String validationErrors);

    /**
     * 检查AI纠错是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
}
