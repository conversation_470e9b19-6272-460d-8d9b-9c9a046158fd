package com.matchmaking.service;

import com.matchmaking.dto.OcrResultDTO;

import java.util.List;
import java.util.Map;

/**
 * 数据校验服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ValidationService {

    /**
     * 校验OCR识别结果
     * 
     * @param fields OCR识别的字段数据
     * @return 校验错误列表
     */
    List<OcrResultDTO.ValidationError> validateOcrResult(Map<String, Object> fields);

    /**
     * 校验单个字段
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @param allFields 所有字段（用于关联校验）
     * @return 校验错误，无错误返回null
     */
    OcrResultDTO.ValidationError validateField(String fieldName, Object value, Map<String, Object> allFields);

    /**
     * 获取字段的有效值范围
     * 
     * @param fieldName 字段名
     * @return 有效值范围描述
     */
    String getFieldValidRange(String fieldName);
}
