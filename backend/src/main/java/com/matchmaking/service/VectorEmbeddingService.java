package com.matchmaking.service;

import com.matchmaking.entity.Member;

import java.util.List;
import java.util.Map;

/**
 * 向量化服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface VectorEmbeddingService {

    /**
     * 将文本转换为向量
     * 
     * @param text 输入文本
     * @return 向量数组
     */
    List<Double> textToVector(String text);

    /**
     * 批量文本向量化
     * 
     * @param texts 文本列表
     * @return 向量列表
     */
    List<List<Double>> batchTextToVector(List<String> texts);

    /**
     * 生成会员画像文本
     * 
     * @param member 会员信息
     * @return 画像文本
     */
    String generateMemberProfileText(Member member);

    /**
     * 计算向量相似度
     * 
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度分数 (0-1)
     */
    double calculateCosineSimilarity(List<Double> vector1, List<Double> vector2);

    /**
     * 批量计算相似度
     * 
     * @param targetVector 目标向量
     * @param candidateVectors 候选向量列表
     * @return 相似度分数列表
     */
    List<Double> batchCalculateSimilarity(List<Double> targetVector, List<List<Double>> candidateVectors);

    /**
     * 更新会员向量
     * 
     * @param member 会员信息
     * @return 是否成功
     */
    boolean updateMemberVector(Member member);

    /**
     * 批量更新会员向量
     * 
     * @param members 会员列表
     * @return 成功更新的数量
     */
    int batchUpdateMemberVectors(List<Member> members);

    /**
     * 获取向量化模型信息
     * 
     * @return 模型信息
     */
    Map<String, Object> getModelInfo();

    /**
     * 检查向量化服务是否可用
     * 
     * @return 是否可用
     */
    boolean isServiceAvailable();
}
