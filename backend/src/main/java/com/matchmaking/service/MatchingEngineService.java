package com.matchmaking.service;

import com.matchmaking.dto.MatchRequest;
import com.matchmaking.dto.MatchResult;
import com.matchmaking.entity.Member;

import java.util.List;

/**
 * 智能匹配引擎服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface MatchingEngineService {

    /**
     * 获取匹配推荐
     * 
     * @param request 匹配请求
     * @return 匹配结果列表
     */
    List<MatchResult> getMatches(MatchRequest request);

    /**
     * 计算两个会员的匹配分数
     * 
     * @param memberA 会员A
     * @param memberB 会员B
     * @return 匹配分数
     */
    double calculateMatchScore(Member memberA, Member memberB);

    /**
     * 更新会员画像向量
     * 
     * @param member 会员信息
     * @return 是否成功
     */
    boolean updateMemberEmbedding(Member member);

    /**
     * 批量更新会员画像向量
     * 
     * @param members 会员列表
     * @return 成功更新的数量
     */
    int batchUpdateMemberEmbeddings(List<Member> members);

    /**
     * 语义相似度检索
     * 
     * @param targetMemberId 目标会员ID
     * @param candidatePoolSize 候选池大小
     * @return 相似会员ID和相似度列表
     */
    List<SimilarityResult> semanticSimilaritySearch(Long targetMemberId, int candidatePoolSize);

    /**
     * 相似度结果
     */
    class SimilarityResult {
        private Long memberId;
        private Double similarity;

        public SimilarityResult(Long memberId, Double similarity) {
            this.memberId = memberId;
            this.similarity = similarity;
        }

        public Long getMemberId() {
            return memberId;
        }

        public Double getSimilarity() {
            return similarity;
        }
    }
}
