package com.matchmaking.service;

import com.matchmaking.dto.OcrResultDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * OCR识别服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface OcrService {

    /**
     * 识别表格图片
     * 
     * @param imageFile 图片文件
     * @return OCR识别结果
     */
    OcrResultDTO recognizeForm(MultipartFile imageFile);

    /**
     * 识别表格图片（通过URL）
     * 
     * @param imageUrl 图片URL
     * @return OCR识别结果
     */
    OcrResultDTO recognizeForm(String imageUrl);
}
