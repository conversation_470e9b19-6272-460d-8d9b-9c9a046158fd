package com.matchmaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.matchmaking.entity.MatchRecord;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 匹配记录服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface MatchRecordService {

    /**
     * 提交匹配反馈
     * 
     * @param matchId 匹配记录ID
     * @param action 操作类型：view, reply, meet, success, reject
     * @param feedback 反馈信息
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean submitFeedback(Long matchId, String action, String feedback, Long operatorId);

    /**
     * 分页查询匹配记录
     * 
     * @param page 分页参数
     * @param memberId 会员ID
     * @param status 状态
     * @param matchmakerId 红娘ID
     * @return 匹配记录分页
     */
    IPage<Map<String, Object>> getMatchRecords(Page<Map<String, Object>> page, 
                                               Long memberId, Integer status, Long matchmakerId);

    /**
     * 获取匹配统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param matchmakerId 红娘ID
     * @return 统计数据
     */
    Map<String, Object> getMatchStatistics(LocalDateTime startTime, LocalDateTime endTime, Long matchmakerId);

    /**
     * 获取今日统计数据
     * 
     * @param matchmakerId 红娘ID
     * @return 今日统计
     */
    Map<String, Object> getTodayStatistics(Long matchmakerId);

    /**
     * 获取会员匹配历史
     * 
     * @param memberId 会员ID
     * @param limit 限制数量
     * @return 匹配历史
     */
    java.util.List<MatchRecord> getMemberMatchHistory(Long memberId, Integer limit);
}
