package com.matchmaking.dto;

import com.matchmaking.entity.Member;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 匹配结果DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class MatchResult {

    /**
     * 匹配记录ID
     */
    private Long matchId;

    /**
     * 候选会员信息
     */
    private Member candidate;

    /**
     * 综合匹配分数
     */
    private BigDecimal matchScore;

    /**
     * 语义相似度
     */
    private BigDecimal semanticSimilarity;

    /**
     * 硬性条件分数
     */
    private BigDecimal hardConditionsScore;

    /**
     * 活跃度分数
     */
    private BigDecimal activityScore;

    /**
     * AI生成的推荐理由
     */
    private String reason;

    /**
     * 破冰话术
     */
    private List<String> iceBreakers;

    /**
     * 约会建议
     */
    private List<String> dateSuggestions;

    /**
     * 匹配状态
     */
    private Integer status;

    /**
     * 匹配状态描述
     */
    private String statusDescription;
}
