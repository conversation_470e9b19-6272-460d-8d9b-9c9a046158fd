package com.matchmaking.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * OCR识别结果DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class OcrResultDTO {

    /**
     * 识别置信度
     */
    private BigDecimal confidence;

    /**
     * 识别的字段数据
     */
    private Map<String, Object> fields;

    /**
     * 校验错误列表
     */
    private List<ValidationError> validationErrors;

    /**
     * AI纠错建议
     */
    private Map<String, Object> aiCorrections;

    /**
     * 原始OCR响应
     */
    private String rawResponse;

    @Data
    public static class ValidationError {
        /**
         * 字段名
         */
        private String fieldName;

        /**
         * 错误类型
         */
        private String errorType;

        /**
         * 错误消息
         */
        private String errorMessage;

        /**
         * 原始值
         */
        private Object originalValue;

        /**
         * 建议值
         */
        private Object suggestedValue;
    }
}
