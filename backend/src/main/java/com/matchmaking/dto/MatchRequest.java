package com.matchmaking.dto;

import lombok.Data;

/**
 * 匹配请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class MatchRequest {

    /**
     * 目标会员ID
     */
    private Long memberId;

    /**
     * 返回匹配数量
     */
    private Integer topK = 10;

    /**
     * 红娘ID
     */
    private Long matchmakerId;

    /**
     * 是否强制重新计算（忽略缓存）
     */
    private Boolean forceRefresh = false;

    /**
     * 是否包含AI生成内容
     */
    private Boolean includeAiContent = true;

    /**
     * 过滤条件
     */
    private MatchFilter filter;

    @Data
    public static class MatchFilter {
        /**
         * 最小匹配分数
         */
        private Double minScore;

        /**
         * 排除的会员ID列表
         */
        private java.util.List<Long> excludeMemberIds;

        /**
         * 只包含指定城市
         */
        private String city;

        /**
         * 年龄范围
         */
        private Integer ageMin;
        private Integer ageMax;
    }
}
