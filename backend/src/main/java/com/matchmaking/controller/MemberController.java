package com.matchmaking.controller;

import com.matchmaking.common.Result;
import com.matchmaking.dto.OcrResultDTO;
import com.matchmaking.entity.Member;
import com.matchmaking.service.MemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 会员信息控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/member")
@CrossOrigin(origins = "*")
public class MemberController {

    @Resource
    private MemberService memberService;

    /**
     * 上传表格图片并进行OCR识别
     * 
     * 流程：上传图片 → Qwen3-VL OCR识别 → 规则引擎校验 → AI纠错 → 返回结果
     */
    @PostMapping("/upload-form")
    public Result<OcrResultDTO> uploadForm(@RequestParam("file") MultipartFile file,
                                          @RequestParam(value = "operator", defaultValue = "system") String operator) {
        try {
            log.info("接收到表格上传请求，文件名: {}, 操作人: {}", file.getOriginalFilename(), operator);
            
            // 参数校验
            if (file.isEmpty()) {
                return Result.badRequest("请选择要上传的文件");
            }
            
            // 文件类型校验
            String filename = file.getOriginalFilename();
            if (filename == null || (!filename.toLowerCase().endsWith(".jpg") && 
                                   !filename.toLowerCase().endsWith(".jpeg") && 
                                   !filename.toLowerCase().endsWith(".png") && 
                                   !filename.toLowerCase().endsWith(".pdf"))) {
                return Result.badRequest("只支持JPG、PNG、PDF格式的文件");
            }
            
            // 文件大小校验（10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return Result.badRequest("文件大小不能超过10MB");
            }
            
            // 处理表格图片：OCR识别 → 规则校验 → AI纠错
            OcrResultDTO result = memberService.processFormImage(file, operator);
            
            log.info("表格处理完成，识别字段数: {}, 校验错误数: {}", 
                result.getFields().size(), 
                result.getValidationErrors() != null ? result.getValidationErrors().size() : 0);
            
            return Result.success("表格识别完成", result);
            
        } catch (Exception e) {
            log.error("上传表格失败", e);
            return Result.error("上传表格失败: " + e.getMessage());
        }
    }

    /**
     * 确认并保存会员信息
     * 
     * 流程：人工确认修改 → 数据入库 → 记录审计日志
     */
    @PostMapping("/confirm-save")
    public Result<Member> confirmAndSave(@RequestBody Map<String, Object> request) {
        try {
            // 提取参数
            @SuppressWarnings("unchecked")
            Map<String, Object> fields = (Map<String, Object>) request.get("fields");
            Long rawOcrDataId = request.get("rawOcrDataId") != null ? 
                Long.valueOf(request.get("rawOcrDataId").toString()) : null;
            String operator = request.get("operator") != null ? 
                request.get("operator").toString() : "system";
            
            log.info("接收到会员确认保存请求，操作人: {}, 原始数据ID: {}", operator, rawOcrDataId);
            
            // 参数校验
            if (fields == null || fields.isEmpty()) {
                return Result.badRequest("会员信息不能为空");
            }
            
            // 必填字段校验
            if (fields.get("name") == null || fields.get("name").toString().trim().isEmpty()) {
                return Result.badRequest("姓名不能为空");
            }
            if (fields.get("gender") == null) {
                return Result.badRequest("性别不能为空");
            }
            if (fields.get("age") == null) {
                return Result.badRequest("年龄不能为空");
            }
            if (fields.get("marriageStatus") == null) {
                return Result.badRequest("婚姻状况不能为空");
            }
            
            // 确认并保存会员信息
            Member member = memberService.confirmAndSaveMember(fields, rawOcrDataId, operator);
            
            log.info("会员信息保存成功，ID: {}, 姓名: {}", member.getId(), member.getName());
            
            return Result.success("会员信息保存成功", member);
            
        } catch (Exception e) {
            log.error("确认保存会员信息失败", e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询会员信息
     */
    @GetMapping("/{id}")
    public Result<Member> getMemberById(@PathVariable Long id) {
        try {
            Member member = memberService.getById(id);
            if (member == null) {
                return Result.notFound();
            }
            return Result.success(member);
        } catch (Exception e) {
            log.error("查询会员信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新会员信息
     */
    @PutMapping("/{id}")
    public Result<Member> updateMember(@PathVariable Long id, 
                                      @RequestBody Member member,
                                      @RequestParam(value = "operator", defaultValue = "system") String operator) {
        try {
            member.setId(id);
            boolean success = memberService.updateById(member);
            if (success) {
                // 记录审计日志
                memberService.recordAuditLog(id, "MEMBER_UPDATE", null, "会员信息更新", 
                    4, operator, 2, "手动更新");
                return Result.success("更新成功", member);
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新会员信息失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除会员信息（逻辑删除）
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteMember(@PathVariable Long id,
                                    @RequestParam(value = "operator", defaultValue = "system") String operator) {
        try {
            Member member = memberService.getById(id);
            if (member == null) {
                return Result.notFound();
            }
            
            // 逻辑删除
            member.setStatus(3); // 删除状态
            boolean success = memberService.updateById(member);
            
            if (success) {
                // 记录审计日志
                memberService.recordAuditLog(id, "MEMBER_DELETE", "正常", "删除", 
                    4, operator, 2, "逻辑删除");
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除会员信息失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("服务正常运行");
    }
}
