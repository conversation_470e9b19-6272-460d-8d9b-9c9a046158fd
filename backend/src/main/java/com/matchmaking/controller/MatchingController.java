package com.matchmaking.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.matchmaking.common.Result;
import com.matchmaking.dto.MatchRequest;
import com.matchmaking.dto.MatchResult;
import com.matchmaking.entity.MatchRecord;
import com.matchmaking.service.MatchRecordService;
import com.matchmaking.service.MatchingEngineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 智能匹配控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/matching")
@RequiredArgsConstructor
@Api(tags = "智能匹配管理")
public class MatchingController {

    private final MatchingEngineService matchingEngineService;
    private final MatchRecordService matchRecordService;

    @PostMapping("/matches")
    @ApiOperation("获取匹配推荐")
    public Result<List<MatchResult>> getMatches(@Valid @RequestBody MatchRequest request) {
        try {
            List<MatchResult> matches = matchingEngineService.getMatches(request);
            return Result.success(matches);
        } catch (Exception e) {
            log.error("获取匹配推荐失败", e);
            return Result.error("获取匹配推荐失败: " + e.getMessage());
        }
    }

    @PostMapping("/feedback")
    @ApiOperation("提交匹配反馈")
    public Result<Boolean> submitFeedback(
            @ApiParam("匹配记录ID") @RequestParam Long matchId,
            @ApiParam("操作类型：view, reply, meet, success, reject") @RequestParam String action,
            @ApiParam("反馈信息") @RequestParam(required = false) String feedback,
            @ApiParam("操作人ID") @RequestParam Long operatorId) {
        
        boolean success = matchRecordService.submitFeedback(matchId, action, feedback, operatorId);
        return success ? Result.success(true) : Result.error("提交反馈失败");
    }

    @GetMapping("/records")
    @ApiOperation("分页查询匹配记录")
    public Result<IPage<Map<String, Object>>> getMatchRecords(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("会员ID") @RequestParam(required = false) Long memberId,
            @ApiParam("状态") @RequestParam(required = false) Integer status,
            @ApiParam("红娘ID") @RequestParam(required = false) Long matchmakerId) {
        
        Page<Map<String, Object>> page = new Page<>(current, size);
        IPage<Map<String, Object>> result = matchRecordService.getMatchRecords(page, memberId, status, matchmakerId);
        return Result.success(result);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取匹配统计数据")
    public Result<Map<String, Object>> getStatistics(
            @ApiParam("开始时间 yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间 yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) String endTime,
            @ApiParam("红娘ID") @RequestParam(required = false) Long matchmakerId) {
        
        try {
            LocalDateTime start = startTime != null ? 
                    LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
                    LocalDateTime.now().minusDays(30);
            
            LocalDateTime end = endTime != null ?
                    LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
                    LocalDateTime.now();

            Map<String, Object> statistics = matchRecordService.getMatchStatistics(start, end, matchmakerId);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics/today")
    @ApiOperation("获取今日统计数据")
    public Result<Map<String, Object>> getTodayStatistics(
            @ApiParam("红娘ID") @RequestParam(required = false) Long matchmakerId) {
        
        Map<String, Object> statistics = matchRecordService.getTodayStatistics(matchmakerId);
        return Result.success(statistics);
    }

    @GetMapping("/member/{memberId}/history")
    @ApiOperation("获取会员匹配历史")
    public Result<List<MatchRecord>> getMemberMatchHistory(
            @ApiParam("会员ID") @PathVariable Long memberId,
            @ApiParam("限制数量") @RequestParam(defaultValue = "20") Integer limit) {
        
        List<MatchRecord> history = matchRecordService.getMemberMatchHistory(memberId, limit);
        return Result.success(history);
    }

    @PostMapping("/member/{memberId}/embedding/update")
    @ApiOperation("更新会员画像向量")
    public Result<Boolean> updateMemberEmbedding(@ApiParam("会员ID") @PathVariable Long memberId) {
        // TODO: 实现向量更新逻辑
        return Result.success(true);
    }

    @PostMapping("/embeddings/batch-update")
    @ApiOperation("批量更新会员画像向量")
    public Result<Integer> batchUpdateEmbeddings(@RequestBody List<Long> memberIds) {
        // TODO: 实现批量向量更新逻辑
        return Result.success(memberIds.size());
    }
}
