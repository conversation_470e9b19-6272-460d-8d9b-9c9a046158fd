package com.matchmaking.controller;

import com.matchmaking.common.Result;
import com.matchmaking.entity.Member;
import com.matchmaking.service.ContentGeneratorService;
import com.matchmaking.service.MemberService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI内容生成控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/content")
@Slf4j
public class ContentController {

    @Autowired
    private ContentGeneratorService contentGeneratorService;

    @Autowired
    private MemberService memberService;

    /**
     * 生成匹配推荐理由
     */
    @PostMapping("/match-reason")
    public Result<String> generateMatchReason(@RequestBody MatchReasonRequest request) {
        try {
            Member memberA = memberService.getById(request.getMemberAId());
            Member memberB = memberService.getById(request.getMemberBId());
            
            if (memberA == null || memberB == null) {
                return Result.error("会员信息不存在");
            }
            
            String reason = contentGeneratorService.generateMatchReason(memberA, memberB);
            return Result.success(reason);
            
        } catch (Exception e) {
            log.error("生成匹配推荐理由失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成破冰开场白
     */
    @PostMapping("/ice-breakers")
    public Result<List<String>> generateIceBreakers(@RequestBody IceBreakersRequest request) {
        try {
            Member memberA = memberService.getById(request.getMemberAId());
            Member memberB = memberService.getById(request.getMemberBId());
            
            if (memberA == null || memberB == null) {
                return Result.error("会员信息不存在");
            }
            
            List<String> iceBreakers = contentGeneratorService.generateIceBreakers(memberA, memberB);
            return Result.success(iceBreakers);
            
        } catch (Exception e) {
            log.error("生成破冰开场白失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成约会建议
     */
    @PostMapping("/date-suggestions")
    public Result<List<String>> generateDateSuggestions(@RequestBody DateSuggestionsRequest request) {
        try {
            Member memberA = memberService.getById(request.getMemberAId());
            Member memberB = memberService.getById(request.getMemberBId());
            
            if (memberA == null || memberB == null) {
                return Result.error("会员信息不存在");
            }
            
            String city = request.getCity();
            if (city == null || city.trim().isEmpty()) {
                // 默认使用会员A的城市
                city = memberA.getCity();
            }
            
            List<String> suggestions = contentGeneratorService.generateDateSuggestions(memberA, memberB, city);
            return Result.success(suggestions);
            
        } catch (Exception e) {
            log.error("生成约会建议失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成个人介绍优化建议
     */
    @PostMapping("/profile-optimization")
    public Result<String> generateProfileOptimization(@RequestBody ProfileOptimizationRequest request) {
        try {
            Member member = memberService.getById(request.getMemberId());
            
            if (member == null) {
                return Result.error("会员信息不存在");
            }
            
            String currentProfile = member.getSelfDescription();
            if (currentProfile == null || currentProfile.trim().isEmpty()) {
                currentProfile = "暂无个人介绍";
            }
            
            String memberInfo = formatMemberBasicInfo(member);
            String optimization = contentGeneratorService.generateProfileOptimization(currentProfile, memberInfo);
            
            return Result.success(optimization);
            
        } catch (Exception e) {
            log.error("生成个人介绍优化建议失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 批量生成匹配内容（推荐理由+开场白+约会建议）
     */
    @PostMapping("/match-content-batch")
    public Result<Map<String, Object>> generateMatchContentBatch(@RequestBody MatchContentBatchRequest request) {
        try {
            Member memberA = memberService.getById(request.getMemberAId());
            Member memberB = memberService.getById(request.getMemberBId());
            
            if (memberA == null || memberB == null) {
                return Result.error("会员信息不存在");
            }
            
            Map<String, Object> content = new HashMap<>();
            
            // 生成推荐理由
            if (request.isIncludeReason()) {
                String reason = contentGeneratorService.generateMatchReason(memberA, memberB);
                content.put("matchReason", reason);
            }
            
            // 生成开场白
            if (request.isIncludeIceBreakers()) {
                List<String> iceBreakers = contentGeneratorService.generateIceBreakers(memberA, memberB);
                content.put("iceBreakers", iceBreakers);
            }
            
            // 生成约会建议
            if (request.isIncludeDateSuggestions()) {
                String city = request.getCity();
                if (city == null || city.trim().isEmpty()) {
                    city = memberA.getCity();
                }
                List<String> suggestions = contentGeneratorService.generateDateSuggestions(memberA, memberB, city);
                content.put("dateSuggestions", suggestions);
            }
            
            return Result.success(content);
            
        } catch (Exception e) {
            log.error("批量生成匹配内容失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 检查AI内容生成服务状态
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> checkHealth() {
        Map<String, Object> status = new HashMap<>();
        status.put("available", contentGeneratorService.isAvailable());
        status.put("service", "ContentGeneratorService");
        status.put("timestamp", System.currentTimeMillis());
        
        return Result.success(status);
    }

    /**
     * 格式化会员基本信息
     */
    private String formatMemberBasicInfo(Member member) {
        StringBuilder info = new StringBuilder();
        info.append("姓名：").append(member.getName())
            .append("，年龄：").append(member.getAge())
            .append("，城市：").append(member.getCity())
            .append("，学历：").append(getEducationText(member.getEducationLevel()))
            .append("，职业：").append(member.getOccupation());
        
        if (member.getHobbies() != null && !member.getHobbies().trim().isEmpty()) {
            info.append("，兴趣爱好：").append(member.getHobbies());
        }
        
        return info.toString();
    }

    /**
     * 将学历代码转换为文本
     */
    private String getEducationText(Integer educationLevel) {
        if (educationLevel == null) return "未知";
        
        switch (educationLevel) {
            case 1: return "高中";
            case 2: return "专科";
            case 3: return "本科";
            case 4: return "硕士";
            case 5: return "博士";
            case 6: return "其他";
            default: return "未知";
        }
    }

    // 请求DTO类
    @Data
    public static class MatchReasonRequest {
        private Long memberAId;
        private Long memberBId;
    }

    @Data
    public static class IceBreakersRequest {
        private Long memberAId;
        private Long memberBId;
    }

    @Data
    public static class DateSuggestionsRequest {
        private Long memberAId;
        private Long memberBId;
        private String city;
    }

    @Data
    public static class ProfileOptimizationRequest {
        private Long memberId;
    }

    @Data
    public static class MatchContentBatchRequest {
        private Long memberAId;
        private Long memberBId;
        private String city;
        private boolean includeReason = true;
        private boolean includeIceBreakers = true;
        private boolean includeDateSuggestions = true;
    }
}
