package com.matchmaking.controller;

import com.matchmaking.common.Result;
import com.matchmaking.entity.Member;
import com.matchmaking.entity.MemberPreference;
import com.matchmaking.mapper.MemberMapper;
import com.matchmaking.mapper.MemberPreferenceMapper;
import com.matchmaking.service.VectorEmbeddingService;
import com.matchmaking.util.TestDataGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测试数据管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/test-data")
@RequiredArgsConstructor
@Api(tags = "测试数据管理")
public class TestDataController {

    private final TestDataGenerator testDataGenerator;
    private final MemberMapper memberMapper;
    private final MemberPreferenceMapper memberPreferenceMapper;
    private final VectorEmbeddingService vectorEmbeddingService;

    @PostMapping("/members/generate")
    @ApiOperation("生成测试会员数据")
    public Result<Map<String, Object>> generateTestMembers(
            @ApiParam("生成数量") @RequestParam(defaultValue = "50") Integer count) {
        
        try {
            if (count > 200) {
                return Result.error("单次生成数量不能超过200个");
            }

            // 生成测试会员
            List<Member> members = testDataGenerator.generateTestMembers(count);
            
            // 批量插入数据库
            int insertedCount = 0;
            for (Member member : members) {
                try {
                    memberMapper.insert(member);
                    insertedCount++;
                } catch (Exception e) {
                    log.error("插入会员数据失败: {}", member.getName(), e);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("generated", count);
            result.put("inserted", insertedCount);
            result.put("success", insertedCount > 0);

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成测试会员数据失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    @PostMapping("/preferences/generate")
    @ApiOperation("为现有会员生成偏好配置")
    public Result<Map<String, Object>> generateTestPreferences() {
        try {
            // 获取所有没有偏好配置的会员
            List<Member> members = memberMapper.selectList(null);
            
            int generatedCount = 0;
            for (Member member : members) {
                // 检查是否已有偏好配置
                MemberPreference existing = memberPreferenceMapper.selectById(member.getId());
                if (existing == null) {
                    MemberPreference preference = testDataGenerator.generateTestPreference(
                            member.getId(), member.getGender(), member.getAge());
                    
                    try {
                        memberPreferenceMapper.insert(preference);
                        generatedCount++;
                    } catch (Exception e) {
                        log.error("插入偏好配置失败: 会员ID {}", member.getId(), e);
                    }
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalMembers", members.size());
            result.put("generated", generatedCount);
            result.put("success", generatedCount > 0);

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成测试偏好配置失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    @PostMapping("/vectors/generate")
    @ApiOperation("为现有会员生成向量数据")
    public Result<Map<String, Object>> generateTestVectors(
            @ApiParam("批次大小") @RequestParam(defaultValue = "20") Integer batchSize) {
        
        try {
            // 获取所有活跃会员
            List<Member> members = memberMapper.selectList(null);
            
            if (members.isEmpty()) {
                return Result.error("没有找到会员数据");
            }

            int totalMembers = members.size();
            int processedCount = 0;
            int successCount = 0;

            // 分批处理
            for (int i = 0; i < totalMembers; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalMembers);
                List<Member> batch = members.subList(i, endIndex);
                
                try {
                    int batchSuccess = vectorEmbeddingService.batchUpdateMemberVectors(batch);
                    successCount += batchSuccess;
                    processedCount += batch.size();
                    
                    log.info("处理批次 {}-{}, 成功 {} 个", i + 1, endIndex, batchSuccess);
                    
                    // 避免API调用过于频繁
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error("处理批次失败: {}-{}", i + 1, endIndex, e);
                    processedCount += batch.size();
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalMembers", totalMembers);
            result.put("processed", processedCount);
            result.put("success", successCount);
            result.put("successRate", processedCount > 0 ? (double) successCount / processedCount : 0.0);

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成测试向量数据失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    @PostMapping("/complete-dataset")
    @ApiOperation("生成完整测试数据集")
    public Result<Map<String, Object>> generateCompleteDataset(
            @ApiParam("会员数量") @RequestParam(defaultValue = "100") Integer memberCount) {
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 1. 生成会员数据
            log.info("开始生成 {} 个测试会员", memberCount);
            List<Member> members = testDataGenerator.generateTestMembers(memberCount);
            
            int insertedMembers = 0;
            for (Member member : members) {
                try {
                    memberMapper.insert(member);
                    insertedMembers++;
                } catch (Exception e) {
                    log.error("插入会员失败", e);
                }
            }
            result.put("membersGenerated", insertedMembers);

            // 2. 生成偏好配置
            log.info("开始生成偏好配置");
            int insertedPreferences = 0;
            for (Member member : members) {
                if (member.getId() != null) {
                    MemberPreference preference = testDataGenerator.generateTestPreference(
                            member.getId(), member.getGender(), member.getAge());
                    try {
                        memberPreferenceMapper.insert(preference);
                        insertedPreferences++;
                    } catch (Exception e) {
                        log.error("插入偏好配置失败", e);
                    }
                }
            }
            result.put("preferencesGenerated", insertedPreferences);

            // 3. 生成向量数据（分批处理）
            log.info("开始生成向量数据");
            int vectorsGenerated = 0;
            List<Member> membersWithId = members.stream()
                    .filter(m -> m.getId() != null)
                    .collect(Collectors.toList());
            
            if (!membersWithId.isEmpty()) {
                try {
                    vectorsGenerated = vectorEmbeddingService.batchUpdateMemberVectors(membersWithId);
                } catch (Exception e) {
                    log.error("生成向量数据失败", e);
                }
            }
            result.put("vectorsGenerated", vectorsGenerated);

            result.put("success", insertedMembers > 0);
            result.put("message", String.format("成功生成 %d 个会员，%d 个偏好配置，%d 个向量", 
                    insertedMembers, insertedPreferences, vectorsGenerated));

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成完整测试数据集失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/members/clear")
    @ApiOperation("清空测试数据")
    public Result<String> clearTestData() {
        try {
            // 注意：这个操作会清空所有数据，生产环境请谨慎使用
            log.warn("开始清空测试数据");
            
            // 清空相关表（注意外键约束顺序）
            memberPreferenceMapper.delete(null);
            memberMapper.delete(null);
            
            return Result.success("测试数据已清空");
        } catch (Exception e) {
            log.error("清空测试数据失败", e);
            return Result.error("清空失败: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @ApiOperation("获取测试数据状态")
    public Result<Map<String, Object>> getTestDataStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 统计各种数据数量
            long memberCount = memberMapper.selectCount(null);
            long preferenceCount = memberPreferenceMapper.selectCount(null);
            
            status.put("memberCount", memberCount);
            status.put("preferenceCount", preferenceCount);
            status.put("vectorServiceAvailable", vectorEmbeddingService.isServiceAvailable());
            status.put("modelInfo", vectorEmbeddingService.getModelInfo());

            return Result.success(status);
        } catch (Exception e) {
            log.error("获取测试数据状态失败", e);
            return Result.error("获取状态失败: " + e.getMessage());
        }
    }
}
