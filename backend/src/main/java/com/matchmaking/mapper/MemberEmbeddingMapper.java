package com.matchmaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.matchmaking.entity.MemberEmbedding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 会员画像向量Mapper
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface MemberEmbeddingMapper extends BaseMapper<MemberEmbedding> {

    /**
     * 获取所有有效的向量数据
     * 
     * @param vectorVersion 向量版本
     * @return 向量数据列表
     */
    @Select("SELECT member_id, profile_vector FROM member_embeddings WHERE vector_version = #{vectorVersion}")
    List<MemberEmbedding> selectAllVectors(@Param("vectorVersion") String vectorVersion);

    /**
     * 批量插入或更新向量数据
     * 
     * @param embeddings 向量数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("embeddings") List<MemberEmbedding> embeddings);
}
