package com.matchmaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.matchmaking.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 系统配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface SystemConfigMapper extends BaseMapper<SystemConfig> {

    /**
     * 根据配置键获取配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    @Select("SELECT config_value FROM system_config WHERE config_key = #{configKey} AND is_active = 1")
    String getConfigValue(@Param("configKey") String configKey);

    /**
     * 根据配置键获取配置对象
     * 
     * @param configKey 配置键
     * @return 系统配置对象
     */
    @Select("SELECT * FROM system_config WHERE config_key = #{configKey} AND is_active = 1")
    SystemConfig getConfigByKey(@Param("configKey") String configKey);
}
