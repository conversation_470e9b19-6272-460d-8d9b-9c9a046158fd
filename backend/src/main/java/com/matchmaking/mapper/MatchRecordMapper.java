package com.matchmaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.matchmaking.entity.MatchRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 匹配记录Mapper
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface MatchRecordMapper extends BaseMapper<MatchRecord> {

    /**
     * 分页查询匹配记录（带会员信息）
     * 
     * @param page 分页参数
     * @param memberId 会员ID
     * @param status 状态
     * @param matchmakerId 红娘ID
     * @return 匹配记录分页
     */
    IPage<Map<String, Object>> selectMatchRecordsWithMemberInfo(
            Page<Map<String, Object>> page,
            @Param("memberId") Long memberId,
            @Param("status") Integer status,
            @Param("matchmakerId") Long matchmakerId
    );

    /**
     * 获取匹配统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param matchmakerId 红娘ID
     * @return 统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as total_matches, " +
            "SUM(CASE WHEN status >= 1 THEN 1 ELSE 0 END) as viewed_count, " +
            "SUM(CASE WHEN status >= 2 THEN 1 ELSE 0 END) as replied_count, " +
            "SUM(CASE WHEN status >= 3 THEN 1 ELSE 0 END) as met_count, " +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as success_count, " +
            "AVG(match_score) as avg_score " +
            "FROM match_records " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND (#{matchmakerId} IS NULL OR matchmaker_id = #{matchmakerId})")
    Map<String, Object> getMatchStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("matchmakerId") Long matchmakerId
    );

    /**
     * 检查是否已存在匹配记录
     * 
     * @param memberAId 会员A ID
     * @param memberBId 会员B ID
     * @return 匹配记录ID，不存在返回null
     */
    @Select("SELECT id FROM match_records WHERE " +
            "(member_a_id = #{memberAId} AND member_b_id = #{memberBId}) OR " +
            "(member_a_id = #{memberBId} AND member_b_id = #{memberAId})")
    Long selectExistingMatch(@Param("memberAId") Long memberAId, @Param("memberBId") Long memberBId);

    /**
     * 获取会员的匹配历史
     * 
     * @param memberId 会员ID
     * @param limit 限制数量
     * @return 匹配记录列表
     */
    @Select("SELECT * FROM match_records " +
            "WHERE member_a_id = #{memberId} OR member_b_id = #{memberId} " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<MatchRecord> selectMemberMatchHistory(@Param("memberId") Long memberId, @Param("limit") Integer limit);
}
