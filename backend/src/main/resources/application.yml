server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: matchmaking-system
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 日志配置
logging:
  level:
    com.matchmaking: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# 自定义配置
matchmaking:
  # OCR配置
  ocr:
    qwen:
      api-url: https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
      api-key: ${QWEN_API_KEY:your-qwen-api-key}
      model: qwen-vl-plus
      timeout: 30000
  
  # AI纠错配置
  ai:
    deepseek:
      api-url: https://api.deepseek.com/v1/chat/completions
      api-key: ${DEEPSEEK_API_KEY:your-deepseek-api-key}
      model: deepseek-chat
      timeout: 30000
      max-tokens: 1000
  
  # 文件存储配置
  file:
    upload-path: /data/uploads/
    base-url: http://localhost:8080/api/files/
    allowed-types: jpg,jpeg,png,pdf
    max-size: 10485760  # 10MB

  # 规则引擎配置
  validation:
    age-range: 18-80
    height-range: 140-220
    weight-range: 35-200
    ocr-confidence-threshold: 0.8
