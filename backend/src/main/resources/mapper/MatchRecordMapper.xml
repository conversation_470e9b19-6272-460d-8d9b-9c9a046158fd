<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.matchmaking.mapper.MatchRecordMapper">

    <!-- 分页查询匹配记录（带会员信息） -->
    <select id="selectMatchRecordsWithMemberInfo" resultType="java.util.Map">
        SELECT 
            mr.id as match_id,
            mr.member_a_id,
            mr.member_b_id,
            mr.matchmaker_id,
            mr.match_score,
            mr.semantic_similarity,
            mr.reason,
            mr.ice_breakers,
            mr.date_suggestions,
            mr.status,
            mr.feedback,
            mr.viewed_time,
            mr.replied_time,
            mr.met_time,
            mr.result_time,
            mr.create_time,
            mr.update_time,
            ma.name as member_a_name,
            ma.age as member_a_age,
            ma.gender as member_a_gender,
            ma.city as member_a_city,
            mb.name as member_b_name,
            mb.age as member_b_age,
            mb.gender as member_b_gender,
            mb.city as member_b_city,
            u.real_name as matchmaker_name
        FROM match_records mr
        LEFT JOIN member ma ON mr.member_a_id = ma.id
        LEFT JOIN member mb ON mr.member_b_id = mb.id
        LEFT JOIN user u ON mr.matchmaker_id = u.id
        <where>
            <if test="memberId != null">
                AND (mr.member_a_id = #{memberId} OR mr.member_b_id = #{memberId})
            </if>
            <if test="status != null">
                AND mr.status = #{status}
            </if>
            <if test="matchmakerId != null">
                AND mr.matchmaker_id = #{matchmakerId}
            </if>
        </where>
        ORDER BY mr.create_time DESC
    </select>

</mapper>
