# 婚介所后台管理系统 - 完整实现

## 1. 数据库设计（MySQL）

```sql
-- 原始OCR识别数据表
CREATE TABLE `raw_ocr_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `upload_id` varchar(64) NOT NULL COMMENT '上传批次ID',
  `original_image_url` varchar(500) NOT NULL COMMENT '原始图片URL',
  `ocr_result` json NOT NULL COMMENT 'OCR识别原始结果',
  `parsed_data` json NOT NULL COMMENT '解析后的结构化数据',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待校验，1-校验完成，2-已入库',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_upload_id` (`upload_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始OCR识别数据表';

-- 会员信息表
CREATE TABLE `member` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `member_code` varchar(32) NOT NULL COMMENT '会员编号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `gender` tinyint NOT NULL COMMENT '性别：1-男，2-女',
  `age` int NOT NULL COMMENT '年龄',
  `height` int NOT NULL COMMENT '身高(cm)',
  `weight` int DEFAULT NULL COMMENT '体重(kg)',
  `education` varchar(50) NOT NULL COMMENT '学历',
  `occupation` varchar(100) NOT NULL COMMENT '职业',
  `income` decimal(10,2) DEFAULT NULL COMMENT '月收入(万元)',
  `city` varchar(50) NOT NULL COMMENT '所在城市',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `hobbies` text COMMENT '兴趣爱好',
  `requirement` text COMMENT '择偶要求',
  `self_description` text COMMENT '自我介绍',
  `photo_urls` json COMMENT '照片URL数组',
  `registration_date` date NOT NULL COMMENT '登记日期',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-暂停，3-注销',
  `matchmaker_id` bigint DEFAULT NULL COMMENT '红娘ID',
  `source_ocr_id` bigint DEFAULT NULL COMMENT '来源OCR记录ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_code` (`member_code`),
  KEY `idx_name` (`name`),
  KEY `idx_gender_age` (`gender`,`age`),
  KEY `idx_city` (`city`),
  KEY `idx_matchmaker_id` (`matchmaker_id`),
  KEY `idx_source_ocr_id` (`source_ocr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员信息表';

-- 审核日志表
CREATE TABLE `audit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_type` varchar(20) NOT NULL COMMENT '记录类型：ocr_data, member',
  `record_id` bigint NOT NULL COMMENT '关联记录ID',
  `operation` varchar(20) NOT NULL COMMENT '操作类型：create, update, validate, ai_correct',
  `field_name` varchar(50) DEFAULT NULL COMMENT '修改字段名',
  `old_value` text COMMENT '修改前值',
  `new_value` text COMMENT '修改后值',
  `validation_result` json COMMENT '校验结果',
  `ai_correction` json COMMENT 'AI纠错结果',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_type_id` (`record_type`,`record_id`),
  KEY `idx_operation` (`operation`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审核日志表';
```

## 2. 后端代码（Spring Boot 2.x + MyBatis Plus）

### 2.1 项目结构
```
src/main/java/com/matchmaking/
├── MatchmakingApplication.java          # 启动类
├── config/                              # 配置类
│   ├── LangChain4jConfig.java          # LangChain4j配置
│   └── MybatisPlusConfig.java          # MyBatis Plus配置
├── controller/                          # 控制器层
│   └── OcrDataController.java          # OCR数据处理控制器
├── entity/                             # 实体类
│   ├── RawOcrData.java                 # OCR原始数据实体
│   ├── Member.java                     # 会员实体
│   └── AuditLog.java                   # 审核日志实体
├── dto/                                # 数据传输对象
│   ├── OcrResultDTO.java               # OCR结果DTO
│   └── ValidationResultDTO.java        # 校验结果DTO
├── mapper/                             # 数据访问层
│   ├── RawOcrDataMapper.java
│   ├── MemberMapper.java
│   └── AuditLogMapper.java
├── service/                            # 业务服务层
│   ├── OcrService.java                 # OCR服务接口
│   ├── impl/OcrServiceImpl.java        # OCR服务实现
│   ├── ValidationService.java          # 校验服务接口
│   ├── impl/ValidationServiceImpl.java # 校验服务实现
│   ├── AiCorrectionService.java        # AI纠错服务接口
│   └── impl/AiCorrectionServiceImpl.java # AI纠错服务实现
└── util/                               # 工具类
    ├── QwenOcrClient.java              # Qwen3-VL OCR客户端
    └── ValidationRuleEngine.java       # 规则引擎
```

### 2.2 核心代码实现

```java
// MatchmakingApplication.java - 启动类
@SpringBootApplication
@MapperScan("com.matchmaking.mapper")
public class MatchmakingApplication {
    public static void main(String[] args) {
        SpringApplication.run(MatchmakingApplication.class, args);
    }
}

// config/LangChain4jConfig.java - LangChain4j配置
@Configuration
public class LangChain4jConfig {
    
    @Value("${deepseek.api-key}")
    private String apiKey;
    
    @Value("${deepseek.base-url:https://api.deepseek.com}")
    private String baseUrl;
    
    @Bean
    public ChatLanguageModel chatLanguageModel() {
        return OpenAiChatModel.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl + "/v1")
                .modelName("deepseek-chat")
                .temperature(0.3)
                .maxTokens(1000)
                .build();
    }
    
    @Bean
    public AiService<AiCorrectionService> aiCorrectionService(ChatLanguageModel chatLanguageModel) {
        return AiServices.builder(AiCorrectionService.class)
                .chatLanguageModel(chatLanguageModel)
                .build();
    }
}

// entity/RawOcrData.java - OCR原始数据实体
@Data
@TableName("raw_ocr_data")
public class RawOcrData {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String uploadId;
    private String originalImageUrl;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> ocrResult;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> parsedData;
    
    private Integer status; // 0-待校验，1-校验完成，2-已入库
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}

// entity/Member.java - 会员实体
@Data
@TableName("member")
public class Member {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String memberCode;
    private String name;
    private Integer gender;
    private Integer age;
    private Integer height;
    private Integer weight;
    private String education;
    private String occupation;
    private BigDecimal income;
    private String city;
    private String phone;
    private String wechat;
    private String hobbies;
    private String requirement;
    private String selfDescription;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> photoUrls;
    
    private LocalDate registrationDate;
    private Integer status;
    private Long matchmakerId;
    private Long sourceOcrId;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}

// dto/OcrResultDTO.java - OCR结果DTO
@Data
public class OcrResultDTO {
    private String uploadId;
    private String imageUrl;
    private Map<String, Object> rawOcrResult;
    private MemberDataDTO parsedData;
    private ValidationResultDTO validationResult;
    
    @Data
    public static class MemberDataDTO {
        private String name;
        private Integer gender;
        private Integer age;
        private Integer height;
        private Integer weight;
        private String education;
        private String occupation;
        private BigDecimal income;
        private String city;
        private String phone;
        private String wechat;
        private String hobbies;
        private String requirement;
        private String selfDescription;
    }
}

// dto/ValidationResultDTO.java - 校验结果DTO
@Data
public class ValidationResultDTO {
    private boolean isValid;
    private Map<String, String> errors; // 字段名 -> 错误信息
    private Map<String, Object> aiCorrections; // AI建议修正值
    private String aiExplanation; // AI修正说明
}

// util/QwenOcrClient.java - Qwen3-VL OCR客户端
@Component
@Slf4j
public class QwenOcrClient {
    
    @Value("${qwen.api-key}")
    private String apiKey;
    
    @Value("${qwen.base-url}")
    private String baseUrl;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 调用Qwen3-VL OCR识别图片
     * OCR → 校验 → AI 纠错 → 人工确认 → 入库
     */
    public Map<String, Object> recognizeImage(String imageBase64) {
        try {
            // 构造请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "qwen-vl-max");
            
            List<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            
            List<Map<String, Object>> content = new ArrayList<>();
            
            // 添加文本指令
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("type", "text");
            textContent.put("text", "请识别这张婚介所登记表，提取以下字段信息并返回JSON格式：" +
                    "姓名、性别、年龄、身高、体重、学历、职业、月收入、所在城市、联系电话、微信号、兴趣爱好、择偶要求、自我介绍");
            content.add(textContent);
            
            // 添加图片
            Map<String, Object> imageContent = new HashMap<>();
            imageContent.put("type", "image_url");
            Map<String, Object> imageUrl = new HashMap<>();
            imageUrl.put("url", "data:image/jpeg;base64," + imageBase64);
            imageContent.put("image_url", imageUrl);
            content.add(imageContent);
            
            message.put("content", content);
            messages.add(message);
            requestBody.put("messages", messages);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(
                    baseUrl + "/v1/chat/completions", entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                if (!choices.isEmpty()) {
                    Map<String, Object> choice = choices.get(0);
                    Map<String, Object> messageResp = (Map<String, Object>) choice.get("message");
                    String content = (String) messageResp.get("content");
                    
                    // 解析JSON内容
                    ObjectMapper objectMapper = new ObjectMapper();
                    return objectMapper.readValue(content, Map.class);
                }
            }
            
            throw new RuntimeException("OCR识别失败");
            
        } catch (Exception e) {
            log.error("OCR识别异常", e);
            throw new RuntimeException("OCR识别异常: " + e.getMessage());
        }
    }
}

// util/ValidationRuleEngine.java - 规则引擎
@Component
@Slf4j
public class ValidationRuleEngine {
    
    /**
     * 校验会员数据合法性
     * OCR → 校验 → AI 纠错 → 人工确认 → 入库
     */
    public ValidationResultDTO validate(OcrResultDTO.MemberDataDTO memberData) {
        ValidationResultDTO result = new ValidationResultDTO();
        Map<String, String> errors = new HashMap<>();
        
        // 校验姓名
        if (StringUtils.isBlank(memberData.getName()) || memberData.getName().length() > 50) {
            errors.put("name", "姓名不能为空且长度不能超过50字符");
        }
        
        // 校验性别
        if (memberData.getGender() == null || (memberData.getGender() != 1 && memberData.getGender() != 2)) {
            errors.put("gender", "性别必须为1(男)或2(女)");
        }
        
        // 校验年龄
        if (memberData.getAge() == null || memberData.getAge() < 18 || memberData.getAge() > 80) {
            errors.put("age", "年龄必须在18-80岁之间");
        }
        
        // 校验身高
        if (memberData.getHeight() == null || memberData.getHeight() < 140 || memberData.getHeight() > 220) {
            errors.put("height", "身高必须在140-220cm之间");
        }
        
        // 校验体重（可选）
        if (memberData.getWeight() != null && (memberData.getWeight() < 30 || memberData.getWeight() > 200)) {
            errors.put("weight", "体重必须在30-200kg之间");
        }
        
        // 校验学历
        List<String> validEducations = Arrays.asList("小学", "初中", "高中", "中专", "大专", "本科", "硕士", "博士");
        if (StringUtils.isBlank(memberData.getEducation()) || !validEducations.contains(memberData.getEducation())) {
            errors.put("education", "学历必须为：" + String.join("、", validEducations) + "之一");
        }
        
        // 校验收入
        if (memberData.getIncome() != null && (memberData.getIncome().compareTo(BigDecimal.ZERO) < 0 || 
                memberData.getIncome().compareTo(new BigDecimal("100")) > 0)) {
            errors.put("income", "月收入必须在0-100万元之间");
        }
        
        // 校验城市
        if (StringUtils.isBlank(memberData.getCity())) {
            errors.put("city", "所在城市不能为空");
        }
        
        // 校验手机号
        if (StringUtils.isNotBlank(memberData.getPhone()) && !memberData.getPhone().matches("^1[3-9]\\d{9}$")) {
            errors.put("phone", "手机号格式不正确");
        }
        
        result.setValid(errors.isEmpty());
        result.setErrors(errors);
        
        log.info("数据校验完成，有效性: {}, 错误数: {}", result.isValid(), errors.size());
        return result;
    }
}

// service/AiCorrectionService.java - AI纠错服务接口
public interface AiCorrectionService {
    
    @SystemMessage("你是一个专业的婚介所数据校验助手。请根据用户提供的OCR识别结果和校验错误信息，提供合理的修正建议。")
    @UserMessage("""
        请帮我分析以下OCR识别的婚介登记表数据，并针对校验错误提供修正建议：
        
        OCR识别结果：{{ocrData}}
        
        校验错误信息：{{validationErrors}}
        
        请返回JSON格式的修正建议，包含：
        1. corrections: 建议的修正值 (字段名->修正值)
        2. explanation: 修正原因说明
        3. confidence: 修正confidence(0-1)
        
        注意：只对明显的OCR识别错误进行修正，对于合理范围内的数据不要修改。
        """)
    String correctData(
            @V("ocrData") String ocrData,
            @V("validationErrors") String validationErrors
    );
}

// service/impl/OcrServiceImpl.java - OCR服务实现
@Service
@Slf4j
public class OcrServiceImpl implements OcrService {
    
    @Autowired
    private QwenOcrClient qwenOcrClient;
    
    @Autowired
    private ValidationRuleEngine validationRuleEngine;
    
    @Autowired
    private AiCorrectionService aiCorrectionService;
    
    @Autowired
    private RawOcrDataMapper rawOcrDataMapper;
    
    @Autowired
    private AuditLogMapper auditLogMapper;
    
    /**
     * 处理图片OCR识别
     * OCR → 校验 → AI 纠错 → 人工确认 → 入库
     */
    @Override
    @Transactional
    public OcrResultDTO processImageOcr(String imageBase64, String uploadId) {
        try {
            // 步骤1: OCR识别
            log.info("开始OCR识别，uploadId: {}", uploadId);
            Map<String, Object> ocrResult = qwenOcrClient.recognizeImage(imageBase64);
            
            // 步骤2: 解析OCR结果为结构化数据
            OcrResultDTO.MemberDataDTO memberData = parseOcrResult(ocrResult);
            
            // 步骤3: 规则引擎校验
            log.info("开始数据校验");
            ValidationResultDTO validationResult = validationRuleEngine.validate(memberData);
            
            // 步骤4: 如果有校验错误，调用AI纠错
            if (!validationResult.isValid()) {
                log.info("数据校验失败，调用AI纠错");
                String aiCorrectionJson = aiCorrectionService.correctData(
                        JSON.toJSONString(ocrResult),
                        JSON.toJSONString(validationResult.getErrors())
                );
                
                // 解析AI纠错结果
                Map<String, Object> aiCorrection = JSON.parseObject(aiCorrectionJson, Map.class);
                validationResult.setAiCorrections((Map<String, Object>) aiCorrection.get("corrections"));
                validationResult.setAiExplanation((String) aiCorrection.get("explanation"));
                
                // 应用AI建议的修正
                applyAiCorrections(memberData, validationResult.getAiCorrections());
            }
            
            // 步骤5: 保存原始OCR数据
            RawOcrData rawOcrData = new RawOcrData();
            rawOcrData.setUploadId(uploadId);
            rawOcrData.setOriginalImageUrl("temp://upload/" + uploadId);
            rawOcrData.setOcrResult(ocrResult);
            rawOcrData.setParsedData(JSON.parseObject(JSON.toJSONString(memberData), Map.class));
            rawOcrData.setStatus(validationResult.isValid() ? 1 : 0);
            rawOcrDataMapper.insert(rawOcrData);
            
            // 步骤6: 记录审核日志
            AuditLog auditLog = new AuditLog();
            auditLog.setRecordType("ocr_data");
            auditLog.setRecordId(rawOcrData.getId());
            auditLog.setOperation("create");
            auditLog.setValidationResult(JSON.parseObject(JSON.toJSONString(validationResult), Map.class));
            if (!validationResult.isValid()) {
                auditLog.setAiCorrection(JSON.parseObject(JSON.toJSONString(validationResult.getAiCorrections()), Map.class));
            }
            auditLog.setOperatorName("system");
            auditLogMapper.insert(auditLog);
            
            // 构造返回结果
            OcrResultDTO result = new OcrResultDTO();
            result.setUploadId(uploadId);
            result.setImageUrl("temp://upload/" + uploadId);
            result.setRawOcrResult(ocrResult);
            result.setParsedData(memberData);
            result.setValidationResult(validationResult);
            
            log.info("OCR处理完成，uploadId: {}, 校验结果: {}", uploadId, validationResult.isValid());
            return result;
            
        } catch (Exception e) {
            log.error("OCR处理异常，uploadId: {}", uploadId, e);
            throw new RuntimeException("OCR处理失败: " + e.getMessage());
        }
    }
    
    private OcrResultDTO.MemberDataDTO parseOcrResult(Map<String, Object> ocrResult) {
        OcrResultDTO.MemberDataDTO memberData = new OcrResultDTO.MemberDataDTO();
        
        memberData.setName((String) ocrResult.get("姓名"));
        memberData.setGender("男".equals(ocrResult.get("性别")) ? 1 : 2);
        memberData.setAge(parseInteger(ocrResult.get("年龄")));
        memberData.setHeight(parseInteger(ocrResult.get("身高")));
        memberData.setWeight(parseInteger(ocrResult.get("体重")));
        memberData.setEducation((String) ocrResult.get("学历"));
        memberData.setOccupation((String) ocrResult.get("职业"));
        memberData.setIncome(parseBigDecimal(ocrResult.get("月收入")));
        memberData.setCity((String) ocrResult.get("所在城市"));
        memberData.setPhone((String) ocrResult.get("联系电话"));
        memberData.setWechat((String) ocrResult.get("微信号"));
        memberData.setHobbies((String) ocrResult.get("兴趣爱好"));
        memberData.setRequirement((String) ocrResult.get("择偶要求"));
        memberData.setSelfDescription((String) ocrResult.get("自我介绍"));
        
        return memberData;
    }
    
    private void applyAiCorrections(OcrResultDTO.MemberDataDTO memberData, Map<String, Object> corrections) {
        if (corrections == null) return;
        
        corrections.forEach((field, value) -> {
            switch (field) {
                case "name":
                    memberData.setName((String) value);
                    break;
                case "age":
                    memberData.setAge(parseInteger(value));
                    break;
                case "height":
                    memberData.setHeight(parseInteger(value));
                    break;
                case "weight":
                    memberData.setWeight(parseInteger(value));
                    break;
                case "education":
                    memberData.setEducation((String) value);
                    break;
                case "income":
                    memberData.setIncome(parseBigDecimal(value));
                    break;
                case "city":
                    memberData.setCity((String) value);
                    break;
                case "phone":
                    memberData.setPhone((String) value);
                    break;
            }
        });
    }
    
    private Integer parseInteger(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof String) {
            String str = ((String) value).replaceAll("[^0-9]", "");
            return str.isEmpty() ? null : Integer.parseInt(str);
        }
        return null;
    }
    
    private BigDecimal parseBigDecimal(Object value) {
        if (value == null) return null;
        if (value instanceof BigDecimal) return (BigDecimal) value;
        if (value instanceof Number) return new BigDecimal(value.toString());
        if (value instanceof String) {
            String str = ((String) value).replaceAll("[^0-9.]", "");
            return str.isEmpty() ? null : new BigDecimal(str);
        }
        return null;
    }
}

// controller/OcrDataController.java - OCR数据处理控制器
@RestController
@RequestMapping("/api/ocr")
@Slf4j
public class OcrDataController {
    
    @Autowired
    private OcrService ocrService;
    
    @Autowired
    private MemberService memberService;
    
    /**
     * 上传图片进行OCR识别
     * OCR → 校验 → AI 纠错 → 人工确认 → 入库
     */
    @PostMapping("/upload")
    public Result<OcrResultDTO> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件类型
            if (!file.getContentType().startsWith("image/")) {
                return Result.error("只支持图片文件");
            }
            
            // 转换为Base64
            String imageBase64 = Base64.getEncoder().encodeToString(file.getBytes());
            String uploadId = UUID.randomUUID().toString().replace("-", "");
            
            // 调用OCR处理
            OcrResultDTO result = ocrService.processImageOcr(imageBase64, uploadId);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("图片上传处理异常", e);
            return Result.error("图片处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 人工确认并保存会员数据
     * OCR → 校验 → AI 纠错 → 人工确认 → 入库
     */
    @PostMapping("/confirm")
    public Result<Long> confirmMemberData(@RequestBody ConfirmMemberRequest request) {
        try {
            // 保存会员数据
            Long memberId = memberService.saveMemberFromOcr(request);
            return Result.success(memberId);
            
        } catch (Exception e) {
            log.error("确认会员数据异常", e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }
    
    @Data
    public static class ConfirmMemberRequest {
        private String uploadId;
        private OcrResultDTO.MemberDataDTO memberData;
        private String operatorName;
        private String remark;
    }
    
    @Data
    public static class Result<T> {
        private int code;
        private String message;
        private T data;
        
        public static <T> Result<T> success(T data) {
            Result<T> result = new Result<>();
            result.setCode(200);
            result.setMessage("success");
            result.setData(data);
            return result;
        }
        
        public static <T> Result<T> error(String message) {
            Result<T> result = new Result<>();
            result.setCode(500);
            result.setMessage(message);
            return result;
        }
    }
}
```