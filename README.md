# 婚介所后台管理系统（第一阶段）

## 项目概述

本项目是一个基于AI技术的婚介所会员信息录入系统，通过OCR识别、规则引擎校验、AI纠错和人工确认的完整流程，实现表格图片到结构化数据的智能转换。

## 技术架构

### 后端技术栈
- **Spring Boot 2.7.18** - 主框架
- **MyBatis Plus 3.5.3** - ORM框架
- **MySQL 8.0** - 数据库
- **LangChain4j** - AI集成框架
- **Qwen3-VL** - OCR识别
- **DeepSeek** - AI纠错

### 前端技术栈
- **Vue 3** - 前端框架
- **Ant Design Vue 4.0** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Axios** - HTTP客户端

## 核心流程

```mermaid
flowchart TD
    A[上传表格图片] --> B[Qwen3-VL OCR识别]
    B --> C[规则引擎校验]
    C -->|校验通过| D[数据入库]
    C -->|校验失败| E[DeepSeek AI纠错]
    E --> F[人工确认修改]
    F --> D[数据入库]
```

## 项目结构

```
demo1/
├── database/                 # 数据库脚本
│   └── schema.sql           # 数据库表结构
├── backend/                 # Spring Boot后端
│   ├── src/main/java/com/matchmaking/
│   │   ├── controller/      # 控制器层
│   │   ├── service/         # 服务层
│   │   ├── mapper/          # 数据访问层
│   │   ├── entity/          # 实体类
│   │   ├── dto/             # 数据传输对象
│   │   ├── common/          # 公共类
│   │   └── config/          # 配置类
│   └── src/main/resources/
│       └── application.yml  # 配置文件
└── frontend/                # Vue 3前端
    ├── src/
    │   ├── views/           # 页面组件
    │   ├── components/      # 通用组件
    │   ├── api/             # API接口
    │   ├── stores/          # 状态管理
    │   └── router/          # 路由配置
    └── package.json
```

## 快速开始

### 1. 数据库初始化

```bash
# 创建数据库并执行初始化脚本
mysql -u root -p < database/schema.sql
```

### 2. 后端启动

```bash
cd backend

# 配置application.yml中的数据库连接和API密钥
# 修改以下配置：
# - spring.datasource.url/username/password
# - matchmaking.ocr.qwen.api-key
# - matchmaking.ai.deepseek.api-key

# 启动应用
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 3. 前端启动

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 http://localhost:3000 启动

## 核心功能

### 1. 表格图片上传
- 支持JPG、PNG、PDF格式
- 文件大小限制10MB
- 拖拽上传支持

### 2. OCR识别
- 集成Qwen3-VL多模态大模型
- 自动识别表格中的会员信息
- 返回结构化JSON数据

### 3. 规则引擎校验
- 年龄范围：18-80岁
- 身高范围：140-220cm
- 体重范围：35-200kg
- 手机号格式验证
- 枚举值校验（性别、学历、婚姻状况等）

### 4. AI智能纠错
- 使用DeepSeek模型进行二次纠错
- 基于上下文推断正确值
- 提供纠错建议供人工确认

### 5. 人工确认
- 错误字段高亮显示
- 支持手动修改
- AI建议一键采纳
- 实时校验反馈

### 6. 数据入库
- 完整的审计日志记录
- 事务保证数据一致性
- 支持数据追溯

## API接口

### 上传表格识别
```http
POST /api/member/upload-form
Content-Type: multipart/form-data

file: 图片文件
operator: 操作人
```

### 确认保存会员
```http
POST /api/member/confirm-save
Content-Type: application/json

{
  "fields": {...},
  "rawOcrDataId": 123,
  "operator": "红娘账号"
}
```

## 配置说明

### 环境变量
```bash
# Qwen3-VL API密钥
export QWEN_API_KEY=your-qwen-api-key

# DeepSeek API密钥
export DEEPSEEK_API_KEY=your-deepseek-api-key
```

### 应用配置
主要配置项在 `backend/src/main/resources/application.yml`：

- 数据库连接配置
- OCR API配置
- AI纠错API配置
- 文件上传路径配置
- 规则引擎参数配置

## 数据字段

系统支持以下会员信息字段：

| 类别 | 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|------|
| 基础信息 | 姓名 | String | ✓ | 2-10个字符 |
| | 性别 | Integer | ✓ | 1-男，2-女 |
| | 年龄 | Integer | ✓ | 18-80岁 |
| | 出生日期 | Date | | YYYY-MM格式 |
| 外貌信息 | 身高 | Integer | | 140-220cm |
| | 体重 | Integer | | 35-200kg |
| 学历职业 | 学历 | Integer | | 1-6对应不同学历 |
| | 职业 | String | | 自由文本 |
| | 月收入 | Integer | | 1-5对应不同收入区间 |
| 婚姻状况 | 婚姻状况 | Integer | ✓ | 1-未婚，2-离异，3-丧偶 |
| 联系方式 | 手机号 | String | | 11位手机号 |
| | 微信号 | String | | 自由文本 |

## 开发指南

### 添加新字段
1. 修改数据库表结构
2. 更新实体类
3. 修改OCR识别提示词
4. 添加校验规则
5. 更新前端表单

### 集成新的OCR服务
1. 实现 `OcrService` 接口
2. 配置API参数
3. 更新服务注入

### 添加新的校验规则
1. 在 `ValidationServiceImpl` 中添加校验方法
2. 更新字段校验映射
3. 添加错误消息定义

## 部署说明

### 生产环境部署
1. 构建后端JAR包：`mvn clean package`
2. 构建前端静态文件：`npm run build`
3. 配置Nginx反向代理
4. 配置MySQL数据库
5. 配置文件上传目录权限

### Docker部署
```bash
# 构建镜像
docker build -t matchmaking-system .

# 运行容器
docker run -d -p 8080:8080 matchmaking-system
```

## 监控与日志

- 应用日志：`logs/application.log`
- 审计日志：数据库 `audit_log` 表
- 性能监控：Spring Boot Actuator
- 错误追踪：完整的异常堆栈记录

## 常见问题

### Q: OCR识别准确率低怎么办？
A: 
1. 检查图片质量和清晰度
2. 调整OCR提示词
3. 启用AI纠错功能
4. 人工确认修改

### Q: API调用失败怎么处理？
A:
1. 检查API密钥配置
2. 确认网络连接
3. 查看错误日志
4. 检查API配额

### Q: 数据库连接失败？
A:
1. 检查数据库服务状态
2. 确认连接参数
3. 检查防火墙设置
4. 验证用户权限

## 后续规划

- [ ] 批量导入Excel功能
- [ ] 智能推荐匹配算法
- [ ] 移动端支持
- [ ] 多租户支持
- [ ] 数据统计分析
- [ ] 微信小程序集成

## 技术支持

如有问题，请提交Issue或联系开发团队。

## 许可证

本项目采用MIT许可证。
