# 向量化检索系统使用指南

## 🎯 系统概述

向量化检索系统是智能匹配引擎的核心技术，通过将会员信息转换为高维向量，实现基于语义相似度的精准匹配。系统支持文本向量化、相似度计算、批量处理等功能。

## 🏗️ 技术架构

### 核心组件
- **VectorEmbeddingService**: 向量化服务核心接口
- **VectorEmbeddingServiceImpl**: 基于m3e-small模型的实现
- **VectorController**: 向量化管理API
- **TestDataGenerator**: 测试数据生成器
- **VectorDemo**: 前端演示界面

### 数据流程
```
会员信息 → 画像文本生成 → 向量化API → 向量存储 → 相似度检索 → 匹配推荐
```

## 🔧 功能特性

### 1. 文本向量化
- **支持模型**: m3e-small (384维中文向量模型)
- **API接口**: `/api/vector/text-to-vector`
- **备用方案**: 当向量化服务不可用时，自动使用随机向量演示

### 2. 会员画像生成
自动将会员信息转换为结构化文本：
```
姓名：张伟 性别：男 年龄：28岁 城市：杭州 身高：175cm 学历：本科 
职业：软件工程师 收入：4000-8000 婚姻状况：未婚 
个人介绍：性格开朗，喜欢阅读和旅行，希望找到志同道合的另一半。
兴趣爱好：阅读,旅行,摄影
```

### 3. 相似度计算
- **算法**: 余弦相似度 (Cosine Similarity)
- **范围**: 0-1，值越大越相似
- **阈值参考**:
  - 0.8+ : 非常相似
  - 0.6-0.8 : 比较相似  
  - 0.4-0.6 : 有一定相似性
  - 0.4- : 相似度较低

### 4. 批量处理
- **批量向量化**: 支持批量更新会员向量
- **性能优化**: 分批处理，避免API调用过于频繁
- **错误处理**: 单个失败不影响整体处理

## 🚀 使用方式

### 1. 向量化演示页面
访问：`http://localhost:3000/vector-demo`

#### 功能标签页：
- **文本向量化**: 输入文本生成向量
- **相似度计算**: 比较两个文本的相似度
- **会员画像**: 生成会员画像文本和向量
- **测试数据**: 管理测试数据集

### 2. API调用示例

#### 文本向量化
```javascript
const vector = await vectorApi.textToVector('软件工程师，喜欢阅读和旅行')
console.log(vector.data) // [0.1234, -0.5678, ...]
```

#### 相似度计算
```javascript
const similarity = await vectorApi.calculateSimilarity(vector1, vector2)
console.log(similarity.data) // 0.8234
```

#### 更新会员向量
```javascript
const success = await vectorApi.updateMemberVector(1001)
console.log(success.data) // true
```

### 3. 测试数据生成

#### 生成完整数据集
```javascript
const result = await testDataApi.generateCompleteDataset(100)
// 生成100个会员 + 偏好配置 + 向量数据
```

#### 分步生成
```javascript
// 1. 生成会员数据
await testDataApi.generateTestMembers(50)

// 2. 生成偏好配置
await testDataApi.generateTestPreferences()

// 3. 生成向量数据
await testDataApi.generateVectors()
```

## ⚙️ 配置说明

### application.yml配置
```yaml
# 向量化配置
vector:
  model:
    # 向量化API地址 (如果不配置，将使用随机向量演示)
    api-url: ${VECTOR_API_URL:http://localhost:8001/embeddings}
    # 模型名称
    name: m3e-small
    # 向量维度
    dimension: 384
    # 请求超时时间(毫秒)
    timeout: 30000
```

### 环境变量
```bash
# 向量化API地址（可选）
export VECTOR_API_URL=http://localhost:8001/embeddings
```

## 🔌 向量化服务部署

### 方案1: 本地部署m3e-small模型
```bash
# 安装依赖
pip install sentence-transformers fastapi uvicorn

# 创建向量化服务
# vector_service.py
from fastapi import FastAPI
from sentence_transformers import SentenceTransformer
import uvicorn

app = FastAPI()
model = SentenceTransformer('moka-ai/m3e-small')

@app.post("/embeddings")
async def create_embeddings(request: dict):
    texts = request["input"]
    embeddings = model.encode(texts)
    
    return {
        "data": [
            {"embedding": embedding.tolist()}
            for embedding in embeddings
        ]
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)

# 启动服务
python vector_service.py
```

### 方案2: 使用Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY vector_service.py .
EXPOSE 8001

CMD ["python", "vector_service.py"]
```

### 方案3: 演示模式（无需外部服务）
如果不配置`VECTOR_API_URL`，系统将自动使用随机向量进行演示，所有功能正常运行。

## 📊 性能优化

### 1. 缓存策略
- **向量缓存**: 会员向量存储在数据库中，避免重复计算
- **API缓存**: 相同文本的向量化结果可以缓存
- **批量处理**: 减少API调用次数

### 2. 数据库优化
```sql
-- 向量表索引优化
CREATE INDEX idx_member_embeddings_version ON member_embeddings(vector_version);
CREATE INDEX idx_member_embeddings_update_time ON member_embeddings(update_time);
```

### 3. 分批处理
```javascript
// 分批更新向量，避免内存溢出
const batchSize = 20
for (let i = 0; i < members.length; i += batchSize) {
    const batch = members.slice(i, i + batchSize)
    await vectorService.batchUpdateMemberVectors(batch)
    await sleep(1000) // 避免API调用过于频繁
}
```

## 🧪 测试验证

### 1. 功能测试
```javascript
// 测试文本向量化
const vector = await vectorApi.textToVector('测试文本')
console.assert(vector.length === 384, '向量维度应为384')

// 测试相似度计算
const sim1 = await vectorApi.calculateSimilarity(vector, vector)
console.assert(Math.abs(sim1 - 1.0) < 0.001, '相同向量相似度应为1')

// 测试会员向量更新
const success = await vectorApi.updateMemberVector(1)
console.assert(success === true, '向量更新应成功')
```

### 2. 性能测试
```javascript
// 批量处理性能测试
const startTime = Date.now()
const result = await testDataApi.generateVectors(100)
const duration = Date.now() - startTime
console.log(`处理100个会员向量耗时: ${duration}ms`)
console.log(`成功率: ${result.successRate * 100}%`)
```

## 🚨 故障排除

### 常见问题

#### 1. 向量化服务不可用
**现象**: 系统显示"向量化服务不可用"
**解决**: 
- 检查`VECTOR_API_URL`配置
- 确认向量化服务是否启动
- 系统会自动降级到随机向量模式

#### 2. 向量维度不匹配
**现象**: 相似度计算失败
**解决**:
- 确认所有向量维度一致（384维）
- 重新生成不匹配的向量数据

#### 3. 批量处理超时
**现象**: 大批量向量生成失败
**解决**:
- 减小批次大小（默认20个）
- 增加API超时时间
- 检查网络连接稳定性

### 监控指标
- 向量化成功率
- API响应时间
- 相似度计算准确性
- 数据库存储使用量

## 🎉 总结

向量化检索系统为智能匹配提供了强大的语义理解能力，通过将会员信息转换为向量表示，实现了更精准的匹配推荐。系统设计了完善的降级机制，即使在向量化服务不可用的情况下，也能正常运行并提供基础匹配功能。

### 核心优势
1. **语义理解**: 基于深度学习的文本理解
2. **高效检索**: 向量相似度计算快速准确
3. **可扩展性**: 支持不同向量化模型
4. **容错性**: 完善的降级和错误处理机制
5. **易用性**: 提供完整的演示和测试工具

系统已为生产环境做好准备，可根据实际需求调整配置和优化性能。
