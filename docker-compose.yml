version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: matchmaking-mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: matchmaking_system
      MYSQL_USER: matchmaking
      MYSQL_PASSWORD: matchmaking123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - matchmaking-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: matchmaking-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - matchmaking-network

  # 后端应用
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: matchmaking-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: matchmaking
      SPRING_DATASOURCE_PASSWORD: matchmaking123
      SPRING_REDIS_HOST: redis
      QWEN_API_KEY: ${QWEN_API_KEY}
      DEEPSEEK_API_KEY: ${DEEPSEEK_API_KEY}
    ports:
      - "8080:8080"
    volumes:
      - upload_data:/data/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - matchmaking-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: matchmaking-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - matchmaking-network

volumes:
  mysql_data:
  redis_data:
  upload_data:

networks:
  matchmaking-network:
    driver: bridge
